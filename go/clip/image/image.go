package image

import (
	"fmt"
	"image"
	"image/color"
	_ "image/jpeg"
	_ "image/png"
	"os"

	"golang.org/x/image/draw"
)

// ModelInfo contains information about different model architectures
type ModelInfo struct {
	Struct          string
	InputResolution int
}

// ModelInfoMap maps model names to their information
var ModelInfoMap = map[string]ModelInfo{
	"ViT-B-16": {
		Struct:          "ViT-B-16@RoBERTa-wwm-ext-base-chinese",
		InputResolution: 224,
	},
	"ViT-L-14": {
		Struct:          "ViT-L-14@RoBERTa-wwm-ext-base-chinese",
		InputResolution: 224,
	},
	"ViT-L-14-336": {
		Struct:          "ViT-L-14-336@RoBERTa-wwm-ext-base-chinese",
		InputResolution: 336,
	},
	"ViT-H-14": {
		Struct:          "ViT-H-14@RoBERTa-wwm-ext-large-chinese",
		InputResolution: 224,
	},
	"RN50": {
		Struct:          "RN50@RBT3-chinese",
		InputResolution: 224,
	},
}

// ImageTransform preprocesses an image for CLIP model inference
func ImageTransform(imgPath string, targetSize int) ([]float32, error) {
	// Open and decode the image
	file, err := os.Open(imgPath)
	if err != nil {
		return nil, fmt.Errorf("failed to open image: %w", err)
	}
	defer file.Close()

	img, _, err := image.Decode(file)
	if err != nil {
		return nil, fmt.Errorf("failed to decode image: %w", err)
	}

	// Convert to RGB if necessary
	rgbImg := convertToRGB(img)

	// Resize image using bicubic interpolation (CatmullRom is similar to BICUBIC)
	dst := image.NewRGBA(image.Rect(0, 0, targetSize, targetSize))
	draw.CatmullRom.Scale(dst, dst.Bounds(), rgbImg, rgbImg.Bounds(), draw.Over, nil)

	// Convert to CHW format and normalize
	return imageToTensor(dst, targetSize), nil
}

// convertToRGB converts an image to RGB format
func convertToRGB(img image.Image) image.Image {
	bounds := img.Bounds()
	rgbImg := image.NewRGBA(bounds)
	
	for y := bounds.Min.Y; y < bounds.Max.Y; y++ {
		for x := bounds.Min.X; x < bounds.Max.X; x++ {
			originalColor := img.At(x, y)
			r, g, b, a := originalColor.RGBA()
			// Convert from 16-bit to 8-bit
			rgbImg.Set(x, y, color.RGBA{
				R: uint8(r >> 8),
				G: uint8(g >> 8),
				B: uint8(b >> 8),
				A: uint8(a >> 8),
			})
		}
	}
	
	return rgbImg
}

// imageToTensor converts an RGBA image to a normalized tensor in CHW format
func imageToTensor(img *image.RGBA, targetSize int) []float32 {
	// CLIP normalization parameters
	mean := []float32{0.48145466, 0.4578275, 0.40821073}
	std := []float32{0.26862954, 0.26130258, 0.27577711}

	// Create tensor in CHW format: [1, 3, H, W]
	tensor := make([]float32, 1*3*targetSize*targetSize)
	
	bounds := img.Bounds()
	for y := 0; y < targetSize; y++ {
		for x := 0; x < targetSize; x++ {
			if x < bounds.Max.X && y < bounds.Max.Y {
				r, g, b, _ := img.At(x, y).RGBA()
				
				// Convert from 16-bit to [0,1] range
				rNorm := float32(r>>8) / 255.0
				gNorm := float32(g>>8) / 255.0
				bNorm := float32(b>>8) / 255.0
				
				// Apply normalization
				rStd := (rNorm - mean[0]) / std[0]
				gStd := (gNorm - mean[1]) / std[1]
				bStd := (bNorm - mean[2]) / std[2]
				
				// Store in CHW format: [batch, channel, height, width]
				// For batch=0, we have channels at indices:
				// R: 0*targetSize*targetSize + y*targetSize + x
				// G: 1*targetSize*targetSize + y*targetSize + x  
				// B: 2*targetSize*targetSize + y*targetSize + x
				tensor[0*targetSize*targetSize+y*targetSize+x] = rStd
				tensor[1*targetSize*targetSize+y*targetSize+x] = gStd
				tensor[2*targetSize*targetSize+y*targetSize+x] = bStd
			}
		}
	}
	
	return tensor
}

// CreateImageTransform creates a transform function for a specific image size
func CreateImageTransform(imageSize int) func(string) ([]float32, error) {
	return func(imagePath string) ([]float32, error) {
		return ImageTransform(imagePath, imageSize)
	}
}
