go版本

LOG: Go tokenizer vocab size: 21128
LOG: Go [CLS] ID: 101
LOG: Go [SEP] ID: 102
LOG: Go 皮 ID: 4649
Processing image...
Image features shape: [1, 512]
Image features (first 10): [1.2703447 -0.63921344 0.2514597 0.17205086 -0.35020062 0.35015714 0.18487722 0.49108878 0.13646838 -0.10574072]
Image features (last 10): [-0.12958671 -0.0803903 -0.20479979 -0.10090031 -0.16364253 0.09583277 0.224923 -0.12804936 0.5972627 0.32833296]
Processing texts...
LOG: Text '皮卡丘' -> tokens: [皮 卡 丘] -> IDs: [4649 1305 687]
LOG: Final tokens for '皮卡丘' (before padding): [101 4649 1305 687 102]
LOG: Final tokens for '皮卡丘' (after padding): [101 4649 1305 687 102 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0]
LOG: Text '杰尼龟' -> tokens: [杰 尼 龟] -> IDs: [3345 2225 7991]
LOG: Final tokens for '杰尼龟' (before padding): [101 3345 2225 7991 102]
LOG: Final tokens for '杰尼龟' (after padding): [101 3345 2225 7991 102 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0]
LOG: Text '妙蛙种子' -> tokens: [妙 蛙 种 子] -> IDs: [1975 6032 4905 2094]
LOG: Final tokens for '妙蛙种子' (before padding): [101 1975 6032 4905 2094 102]
LOG: Final tokens for '妙蛙种子' (after padding): [101 1975 6032 4905 2094 102 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0]
LOG: Text '小火龙' -> tokens: [小 火 龙] -> IDs: [2207 4125 7987]
LOG: Final tokens for '小火龙' (before padding): [101 2207 4125 7987 102]
LOG: Final tokens for '小火龙' (after padding): [101 2207 4125 7987 102 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0]
LOG: Text '皮卡丘' features (first 10): [4.006169 -0.30580664 -1.1638689 0.8173319 -0.9977439 0.27636868 1.0612345 1.1681004 -0.53972286 -0.8149621]
LOG: Text '皮卡丘' features (last 10): [-1.2106513 0.35354814 -0.4359386 0.1394906 -0.36410147 -0.14259793 -0.8297425 0.06521571 -0.12384912 -0.50479484]
LOG: Text '杰尼龟' features (first 10): [3.6340342 -0.5042059 0.1241714 -0.6642575 0.10094423 1.1408176 1.751521 0.15926409 1.7507694 0.18199429]
LOG: Text '杰尼龟' features (last 10): [-0.95932955 1.2405435 -0.8881738 -0.30237904 -0.46036753 -0.6369312 0.3242971 1.0681626 2.0358894 -0.19971354]
LOG: Text '妙蛙种子' features (first 10): [3.039827 -0.24994928 -1.7219332 1.1934886 -0.24571222 1.1308771 1.1854169 0.8991278 1.3922044 0.71450883]
LOG: Text '妙蛙种子' features (last 10): [-0.30560943 0.6224639 0.1033569 -0.84396577 0.26554713 -1.2600943 -0.99445474 0.16822197 1.3623153 0.06310949]
LOG: Text '小火龙' features (first 10): [2.7946475 -0.8685595 -0.7316406 -0.5119879 -0.41424382 1.4878899 1.1943051 -0.97626317 0.8146711 -0.66926396]
LOG: Text '小火龙' features (last 10): [-0.19033669 1.1520672 -1.288417 -0.9781076 0.48598915 -0.94786996 0.51616013 0.27817762 -0.25490716 -2.1371582]
Text features shape: [4, 512]
Computing similarities...
LOG: Input image features (first 10): [1.2703447 -0.63921344 0.2514597 0.17205086 -0.35020062 0.35015714 0.18487722 0.49108878 0.13646838 -0.10574072]
LOG: Input text features count: 4, feature dim: 512
LOG: Text 0 features (first 10): [4.006169 -0.30580664 -1.1638689 0.8173319 -0.9977439 0.27636868 1.0612345 1.1681004 -0.53972286 -0.8149621]
LOG: Text 1 features (first 10): [3.6340342 -0.5042059 0.1241714 -0.6642575 0.10094423 1.1408176 1.751521 0.15926409 1.7507694 0.18199429]
LOG: Text 2 features (first 10): [3.039827 -0.24994928 -1.7219332 1.1934886 -0.24571222 1.1308771 1.1854169 0.8991278 1.3922044 0.71450883]
LOG: Text 3 features (first 10): [2.7946475 -0.8685595 -0.7316406 -0.5119879 -0.41424382 1.4878899 1.1943051 -0.97626317 0.8146711 -0.66926396]
LOG: Before normalization - Image matrix (first 10): [1.2703447 -0.63921344 0.2514597 0.17205086 -0.35020062 0.35015714 0.18487722 0.49108878 0.13646838 -0.10574072]
LOG: Before normalization - Text matrix (first 10): [4.006169 -0.30580664 -1.1638689 0.8173319 -0.9977439 0.27636868 1.0612345 1.1681004 -0.53972286 -0.8149621]
LOG: After normalization - Image matrix (first 10): [0.086681 -0.043616243 0.01715816 0.01173976 -0.023895672 0.023892706 0.012614956 0.03350907 0.009311816 -0.007215137]
LOG: After normalization - Text matrix (first 10): [0.120072 -0.009165568 -0.03488322 0.02449689 -0.029904159 0.00828326 0.031807084 0.035010044 -0.016176453 -0.024425862]
LOG: Raw logits: [0.4712345 0.4054521 0.4424365 0.39605415]
LOG: Scaled logits: [47.12345 40.54521 44.24365 39.605415]
LOG: After softmax: [0.9451086 0.0013139793 0.05306402 0.0005133809]
Image-text similarity probabilities:
  皮卡丘: 0.945109
  杰尼龟: 0.001314
  妙蛙种子: 0.053064
  小火龙: 0.000513
[[9.45108593e-01 1.31397927e-03 5.30640185e-02 5.13380917e-04]]


python版本

LOG: Raw image features (first 10): [ 1.3385832  -0.5511824   0.21498942  0.10283435 -0.42670578  0.40454334
  0.0845367   0.5173362   0.16825953 -0.12476143]
LOG: Raw image features (last 10): [-0.14087038 -0.04845424 -0.2673562  -0.2554283  -0.16309853 -0.0320432
  0.17361659 -0.10539889  0.543359    0.17949542]
LOG: Normalized image features (first 10): [ 0.0931603  -0.0383602   0.01496245  0.00715688 -0.0296971   0.02815468
  0.00588343  0.03600463  0.01171022 -0.00868292]
LOG: Normalized image features (last 10): [-0.00980404 -0.00337223 -0.01860697 -0.01777684 -0.01135104 -0.00223009
  0.01208305 -0.00733536  0.03781572  0.0124922 ]
(1, 512)
LOG: Tokenized text shape: (4, 52)
LOG: Tokenized text:
LOG: Text '皮卡丘' tokens: [ 101 4649 1305  687  102    0    0    0    0    0    0    0    0    0
    0    0    0    0    0    0    0    0    0    0    0    0    0    0
    0    0    0    0    0    0    0    0    0    0    0    0    0    0
    0    0    0    0    0    0    0    0    0    0]
LOG: Text '杰尼龟' tokens: [ 101 3345 2225 7991  102    0    0    0    0    0    0    0    0    0
    0    0    0    0    0    0    0    0    0    0    0    0    0    0
    0    0    0    0    0    0    0    0    0    0    0    0    0    0
    0    0    0    0    0    0    0    0    0    0]
LOG: Text '妙蛙种子' tokens: [ 101 1975 6032 4905 2094  102    0    0    0    0    0    0    0    0
    0    0    0    0    0    0    0    0    0    0    0    0    0    0
    0    0    0    0    0    0    0    0    0    0    0    0    0    0
    0    0    0    0    0    0    0    0    0    0]
LOG: Text '小火龙' tokens: [ 101 2207 4125 7987  102    0    0    0    0    0    0    0    0    0
    0    0    0    0    0    0    0    0    0    0    0    0    0    0
    0    0    0    0    0    0    0    0    0    0    0    0    0    0
    0    0    0    0    0    0    0    0    0    0]
LOG: Raw text features for '皮卡丘' (first 10): [ 4.006513   -0.30595884 -1.1638527   0.8172527  -0.9979281   0.2765103
  1.0615073   1.1678319  -0.53951466 -0.81520003]
LOG: Raw text features for '皮卡丘' (last 10): [-1.2108879   0.35371512 -0.43641734  0.13934612 -0.36408126 -0.14248498
 -0.8293273   0.0651634  -0.12356124 -0.50498843]
LOG: Raw text features for '杰尼龟' (first 10): [ 3.6339462  -0.5042203   0.12424083 -0.6641635   0.10071205  1.1411092
  1.7510985   0.15953727  1.7512811   0.18203704]
LOG: Raw text features for '杰尼龟' (last 10): [-0.9593944   1.2403239  -0.88825417 -0.30202278 -0.46066463 -0.6367954
  0.3243672   1.0682058   2.0357873  -0.19999056]
LOG: Raw text features for '妙蛙种子' (first 10): [ 3.0393872  -0.24986005 -1.7222962   1.1938117  -0.245941    1.1306117
  1.1851208   0.8990953   1.392166    0.7147765 ]
LOG: Raw text features for '妙蛙种子' (last 10): [-0.30581725  0.6227765   0.10317363 -0.8438093   0.2657821  -1.2603377
 -0.9945103   0.16804296  1.3621618   0.06324494]
LOG: Raw text features for '小火龙' (first 10): [ 2.7945518  -0.8684245  -0.7317943  -0.51209533 -0.41403446  1.4878685
  1.1943913  -0.9760129   0.8145762  -0.66907537]
LOG: Raw text features for '小火龙' (last 10): [-0.19037804  1.1519011  -1.2886593  -0.97803336  0.4858482  -0.94831127
  0.51584625  0.27880424 -0.25515735 -2.1368346 ]
LOG: Before normalization - text features shape: (4, 512)
LOG: Before normalization - text features (first text, first 10): [ 4.006513   -0.30595884 -1.1638527   0.8172527  -0.9979281   0.2765103
  1.0615073   1.1678319  -0.53951466 -0.81520003]
LOG: After normalization - text features (first text, first 10): [ 0.12007832 -0.00916982 -0.03488157  0.0244937  -0.02990868  0.00828723
  0.0318142   0.03500083 -0.01616967 -0.02443218]
(4, 512)
LOG: Raw logits: [[0.4737379  0.40761286 0.44520298 0.4014193 ]]
LOG: Scaled logits: [[47.37379  40.761288 44.520298 40.14193 ]]
LOG: After softmax: [[9.4365501e-01 1.2677676e-03 5.4394763e-02 6.8242650e-04]]
[[9.4365501e-01 1.2677676e-03 5.4394763e-02 6.8242650e-04]]