package tokenizer

import (
	"bufio"
	"fmt"
	"os"
	"strings"
	"unicode"
)

// CharTokenizer implements character-level tokenization matching Python BERT behavior
type CharTokenizer struct {
	vocab map[string]int
}

// CreateCharTokenizer creates a character-level tokenizer that matches Python BERT behavior
func CreateCharTokenizer(vocabFile string) *CharTokenizer {
	// Load vocabulary manually for character-level tokenization
	vocab, err := loadVocab(vocabFile)
	if err != nil {
		panic(fmt.Sprintf("Failed to load vocabulary: %v", err))
	}

	return &CharTokenizer{
		vocab: vocab,
	}
}

// loadVocab loads vocabulary from file
func loadVocab(vocabFile string) (map[string]int, error) {
	file, err := os.Open(vocabFile)
	if err != nil {
		return nil, err
	}
	defer file.Close()

	vocab := make(map[string]int)
	scanner := bufio.NewScanner(file)
	index := 0

	for scanner.Scan() {
		token := strings.TrimSpace(scanner.Text())
		// Don't skip empty lines - Python version doesn't skip them
		vocab[token] = index
		index++
	}

	return vocab, scanner.Err()
}

// TokenizeAndConvertToIDs implements character-level tokenization like Python BERT
func (ct *CharTokenizer) TokenizeAndConvertToIDs(texts []string, contextLength int) ([][]int64, error) {
	// Get [CLS] and [SEP] token IDs
	clsID, ok := ct.vocab["[CLS]"]
	if !ok {
		return nil, fmt.Errorf("token [CLS] not found in vocab")
	}
	sepID, ok := ct.vocab["[SEP]"]
	if !ok {
		return nil, fmt.Errorf("token [SEP] not found in vocab")
	}

	var allTokenIDs [][]int64
	for _, text := range texts {
		// Tokenize using character-level approach like Python BERT
		tokens := ct.tokenizeText(text)

		// Convert tokens to IDs
		var ids []int
		for _, token := range tokens {
			if id, exists := ct.vocab[token]; exists {
				ids = append(ids, id)
			} else {
				// Use [UNK] token if not found
				unkID := ct.vocab["[UNK]"]
				ids = append(ids, unkID)
			}
		}

		// Truncate if necessary
		if len(ids) > contextLength-2 {
			ids = ids[:contextLength-2]
		}

		// Assemble final ID sequence: [CLS] + ids + [SEP]
		finalIDs := make([]int64, 0, contextLength)
		finalIDs = append(finalIDs, int64(clsID))
		for _, id := range ids {
			finalIDs = append(finalIDs, int64(id))
		}
		finalIDs = append(finalIDs, int64(sepID))

		// Padding to contextLength
		for len(finalIDs) < contextLength {
			finalIDs = append(finalIDs, 0) // Assume padding token ID is 0
		}

		allTokenIDs = append(allTokenIDs, finalIDs)
	}

	return allTokenIDs, nil
}

// tokenizeText performs character-level tokenization like Python BERT BasicTokenizer
func (ct *CharTokenizer) tokenizeText(text string) []string {
	// Clean and normalize text
	text = ct.cleanText(text)

	// Add spaces around Chinese characters (like Python _tokenize_chinese_chars)
	text = ct.tokenizeChineseChars(text)

	// Split by whitespace
	tokens := strings.Fields(text)

	// Process each token
	var result []string
	for _, token := range tokens {
		// Convert to lowercase (like Python do_lower_case=True)
		token = strings.ToLower(token)

		// Split on punctuation
		subTokens := ct.splitOnPunctuation(token)
		result = append(result, subTokens...)
	}

	return result
}

// cleanText performs basic text cleaning
func (ct *CharTokenizer) cleanText(text string) string {
	var output strings.Builder
	for _, char := range text {
		cp := int(char)
		if cp == 0 || cp == 0xfffd || ct.isControl(char) {
			continue
		}
		if ct.isWhitespace(char) {
			output.WriteRune(' ')
		} else {
			output.WriteRune(char)
		}
	}
	return output.String()
}

// tokenizeChineseChars adds whitespace around CJK characters
func (ct *CharTokenizer) tokenizeChineseChars(text string) string {
	var output strings.Builder
	for _, char := range text {
		if ct.isChineseChar(int(char)) {
			output.WriteRune(' ')
			output.WriteRune(char)
			output.WriteRune(' ')
		} else {
			output.WriteRune(char)
		}
	}
	return output.String()
}

// splitOnPunctuation splits text on punctuation
func (ct *CharTokenizer) splitOnPunctuation(text string) []string {
	chars := []rune(text)
	if len(chars) == 0 {
		return []string{}
	}

	var output [][]rune
	var currentToken []rune

	for _, char := range chars {
		if ct.isPunctuation(char) {
			if len(currentToken) > 0 {
				output = append(output, currentToken)
				currentToken = []rune{}
			}
			output = append(output, []rune{char})
		} else {
			currentToken = append(currentToken, char)
		}
	}

	if len(currentToken) > 0 {
		output = append(output, currentToken)
	}

	result := make([]string, len(output))
	for i, chars := range output {
		result[i] = string(chars)
	}
	return result
}

// Helper functions for character classification

// isChineseChar checks if a codepoint is a CJK character
func (ct *CharTokenizer) isChineseChar(cp int) bool {
	return (cp >= 0x4E00 && cp <= 0x9FFF) ||
		(cp >= 0x3400 && cp <= 0x4DBF) ||
		(cp >= 0x20000 && cp <= 0x2A6DF) ||
		(cp >= 0x2A700 && cp <= 0x2B73F) ||
		(cp >= 0x2B740 && cp <= 0x2B81F) ||
		(cp >= 0x2B820 && cp <= 0x2CEAF) ||
		(cp >= 0xF900 && cp <= 0xFAFF) ||
		(cp >= 0x2F800 && cp <= 0x2FA1F)
}

// isWhitespace checks if a character is whitespace
func (ct *CharTokenizer) isWhitespace(char rune) bool {
	if char == ' ' || char == '\t' || char == '\n' || char == '\r' {
		return true
	}
	return unicode.Is(unicode.Zs, char)
}

// isControl checks if a character is a control character
func (ct *CharTokenizer) isControl(char rune) bool {
	if char == '\t' || char == '\n' || char == '\r' {
		return false
	}
	return unicode.Is(unicode.Cc, char) || unicode.Is(unicode.Cf, char)
}

// isPunctuation checks if a character is punctuation
func (ct *CharTokenizer) isPunctuation(char rune) bool {
	cp := int(char)
	// ASCII punctuation
	if (cp >= 33 && cp <= 47) || (cp >= 58 && cp <= 64) ||
		(cp >= 91 && cp <= 96) || (cp >= 123 && cp <= 126) {
		return true
	}
	return unicode.Is(unicode.P, char)
}

// GetTokenID returns the ID for a given token
func (ct *CharTokenizer) GetTokenID(token string) (uint32, bool) {
	id, ok := ct.vocab[token]
	return uint32(id), ok
}

// GetVocabSize returns the vocabulary size
func (ct *CharTokenizer) GetVocabSize() int {
	return len(ct.vocab)
}
