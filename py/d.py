import cv2
import numpy as np
import os

def preprocess_image(image):
    """
    预处理图像，去除灰色噪点。
    使用自适应阈值或调整普通二值化参数。
    """
    # 方法1：使用自适应阈值
    binary = cv2.adaptiveThreshold(
        image,
        255,
        cv2.ADAPTIVE_THRESH_GAUSSIAN_C,
        cv2.THRESH_BINARY_INV,
        blockSize=15,  # 必须是奇数
        C=2
    )
    
    # 可选：使用形态学操作清理噪点
    kernel = np.ones((2,2), np.uint8)
    binary = cv2.morphologyEx(binary, cv2.MORPH_OPEN, kernel)
    
    return binary

def crop_margins(image, threshold_ratio=0.01):
    """
    使用垂直和水平投影，自动裁剪图像四周的空白或噪点边缘。

    Args:
        image (numpy.ndarray): 输入的原始图像 (可以是彩色或灰度)。
        threshold_ratio (float): 用于判断是否为内容区域的阈值比例。

    Returns:
        numpy.ndarray: 裁剪后的图像。
    """
    # 直接二值化
    _, binary = cv2.threshold(image, 0, 255, cv2.THRESH_BINARY_INV + cv2.THRESH_OTSU)

    # 垂直投影，找到左右边界
    vertical_projection = np.sum(binary, axis=0)
    v_thresh = np.max(vertical_projection) * threshold_ratio

    x_left = 0
    for i, val in enumerate(vertical_projection):
        if val > v_thresh:
            x_left = i
            break

    x_right = len(vertical_projection) - 1
    for i in range(len(vertical_projection) - 1, -1, -1):
        if vertical_projection[i] > v_thresh:
            x_right = i
            break

    # 水平投影，找到上下边界 (顺便切页眉页脚)
    horizontal_projection = np.sum(binary, axis=1)
    h_thresh = np.max(horizontal_projection) * threshold_ratio

    y_top = 0
    for i, val in enumerate(horizontal_projection):
        if val > h_thresh:
            y_top = i
            break

    y_bottom = len(horizontal_projection) - 1
    for i in range(len(horizontal_projection) - 1, -1, -1):
        if horizontal_projection[i] > h_thresh:
            y_bottom = i
            break

    # 增加一点边距
    padding = 5
    x_left = max(0, x_left - padding)
    x_right = min(image.shape[1], x_right + padding)
    y_top = max(0, y_top - padding)
    y_bottom = min(image.shape[0], y_bottom + padding)

    return image[y_top:y_bottom, x_left:x_right]


def split_image_by_large_gaps(image_path,
                              output_dir='split_large_gaps_output',
                              min_gap_height=20,
                              blank_threshold=5):
    """
    在清洗过边缘的图像上，通过寻找高度超过阈值的"大间隙"来分割词条。

    Args:
        image_path (str): 输入图像的路径。
        output_dir (str): 保存结果的文件夹。
        min_gap_height (int): 定义一个"大间隙"的最小高度。
        blank_threshold (int): 定义空白行的像素和阈值。
    """
    # --- 1. 准备工作 ---
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)

    # 直接读取为灰度图像
    original_image = cv2.imread(image_path, cv2.IMREAD_GRAYSCALE)
    if original_image is None:
        print(f"错误: 无法加载图像 '{image_path}'")
        return

    # --- 2. 【关键步骤】清洗边缘噪点 ---
    print(">>> 步骤 1: 裁剪图像边缘以去除噪点...")
    clean_image = crop_margins(original_image)
    # 保存清洗后的图像，便于调试
    cv2.imwrite(os.path.join(output_dir, "_margin_cropped.png"), clean_image)
    print("    -> 边缘已清洗并保存了中间结果。")

    # --- 3. 在清洗后的图像上进行投影分析 ---
    print(">>> 步骤 2: 在清洗后的图像上寻找大间隙...")
    # 使用预处理函数进行二值化
    binary = preprocess_image(clean_image)
    # 保存二值化图像
    cv2.imwrite(os.path.join(output_dir, "_binary.png"), binary)
    print("    -> 已保存二值化图像。")
    
    horizontal_projection = np.sum(binary, axis=1)

    all_gaps = []
    current_gap_size = 0
    current_gap_start = -1
    for i, proj_val in enumerate(horizontal_projection):
        if proj_val <= blank_threshold:
            if current_gap_size == 0:
                current_gap_start = i
            current_gap_size += 1
        else:
            if current_gap_size > 0:
                all_gaps.append({'start': current_gap_start, 'size': current_gap_size})
            current_gap_size = 0
    if current_gap_size > 0:
        all_gaps.append({'start': current_gap_start, 'size': current_gap_size})

    # --- 4. 【关键步骤】筛选出真正的大间隙 ---
    large_gaps = [g for g in all_gaps if g['size'] >= min_gap_height]

    if not large_gaps:
        print("警告: 未找到高度超过阈值的大间隙，无法切割。")
        # 即使没找到大切割点，也把清洗后的整张图保存下来
        cv2.imwrite(os.path.join(output_dir, "part_01_full.png"), clean_image)
        return

    print(f"    -> 找到 {len(large_gaps)} 个大间隙 (高度 >= {min_gap_height} 像素)。")

    # --- 5. 执行切割与保存 ---
    cut_points = [g['start'] + g['size'] // 2 for g in large_gaps]
    cut_points.sort()
    print(f"    -> 将在以下 y 坐标处切割: {cut_points}")

    last_y = 0
    visual_image = cv2.cvtColor(clean_image, cv2.COLOR_GRAY2BGR)  # 转为彩色以便画彩色线

    for i, cut_y in enumerate(cut_points):
        part = clean_image[last_y:cut_y, :]
        filename = os.path.join(output_dir, f"entry_{i+1:02d}.png")
        cv2.imwrite(filename, part)
        print(f"        保存词条片段: {filename}")
        cv2.line(visual_image, (0, cut_y), (visual_image.shape[1], cut_y), (0, 255, 0), 2)
        last_y = cut_y

    final_part = clean_image[last_y:, :]
    final_filename = os.path.join(output_dir, f"entry_{len(cut_points)+1:02d}.png")
    cv2.imwrite(final_filename, final_part)
    print(f"        保存词条片段: {final_filename}")

    visual_filename = os.path.join(output_dir, "_visualized_large_cuts.png")
    cv2.imwrite(visual_filename, visual_image)
    print(f"    -> 已保存带切割线的可视化图: {visual_filename}")


if __name__ == '__main__':
    INPUT_IMAGE_PATH = 'img.png' # <--- 请将这里换成你的图片文件名

    if not os.path.exists(INPUT_IMAGE_PATH):
        print(f"输入文件不存在: '{INPUT_IMAGE_PATH}'，请检查文件名或路径。")
    else:
        split_image_by_large_gaps(INPUT_IMAGE_PATH)