import onnxruntime as ort
import cv2
import os
import numpy as np

# {0: 'title', 1: 'plain text', 2: 'abandon', 3: 'figure', 4: 'figure_caption', 5: 'table', 6: 'table_caption', 7: 'table_footnote', 8: 'isolate_formula', 9: 'formula_caption'}
class YoloResult:
    def __init__(self, boxes, names):
        self.boxes = [YoloBox(data=d) for d in boxes]
        self.boxes = sorted(self.boxes, key=lambda x: x.conf, reverse=True)
        self.names = names

class YoloBox:
    def __init__(self, data):
        self.xyxy = data[:4]
        self.conf = data[-2]
        self.cls = data[-1]

class TextContentDetectorONNX:
    def __init__(self, model_path="./doclayout_yolo_docstructbench_imgsz1024.onnx", debug_dir="output_data_cv/debug_images"):
        """
        初始化页眉页脚检测器
        Args:
            model_path: ONNX模型路径
            debug_dir: 调试图像输出目录
        """
        print(">>> 正在初始化 ONNX 推理引擎...")
        
        # 创建ONNX运行时会话
        self.session = ort.InferenceSession(model_path)
        
        # 从模型元数据中获取类别名称和步长
        metadata = self.session.get_modelmeta().custom_metadata_map
        self.names = eval(metadata["names"])
        print(self.names)
        self.stride = eval(metadata["stride"])
        
        self.debug_dir = debug_dir
        
        # 确保调试目录存在
        if not os.path.exists(debug_dir):
            os.makedirs(debug_dir)

    def _resize_and_pad_image(self, image, new_shape):
        """调整图像大小并进行填充"""
        if isinstance(new_shape, int):
            new_shape = (new_shape, new_shape)

        h, w = image.shape[:2]
        new_h, new_w = new_shape

        # 计算缩放比例
        r = min(new_h / h, new_w / w)
        resized_h, resized_w = int(round(h * r)), int(round(w * r))

        # 调整图像大小
        image = cv2.resize(image, (resized_w, resized_h), interpolation=cv2.INTER_LINEAR)

        # 计算填充大小并对齐到stride
        pad_w = (new_w - resized_w) % self.stride
        pad_h = (new_h - resized_h) % self.stride
        top, bottom = pad_h // 2, pad_h - pad_h // 2
        left, right = pad_w // 2, pad_w - pad_w // 2

        # 添加填充
        image = cv2.copyMakeBorder(
            image, top, bottom, left, right, cv2.BORDER_CONSTANT, value=(114, 114, 114)
        )

        return image

    def _scale_boxes(self, img1_shape, boxes, img0_shape):
        """调整边界框的大小"""
        # 计算缩放比例
        gain = min(img1_shape[0] / img0_shape[0], img1_shape[1] / img0_shape[1])

        # 计算填充大小
        pad_x = round((img1_shape[1] - img0_shape[1] * gain) / 2 - 0.1)
        pad_y = round((img1_shape[0] - img0_shape[0] * gain) / 2 - 0.1)

        # 移除填充并缩放边界框
        boxes[..., :4] = (boxes[..., :4] - [pad_x, pad_y, pad_x, pad_y]) / gain
        return boxes

    def _inference(self, image):
        """
        对输入图像进行推理
        Args:
            image: OpenCV格式的图像（BGR）
        Returns:
            YoloResult: 包含预测框和类别名称的结果对象
        """
        # 保存原始尺寸
        orig_h, orig_w = image.shape[:2]
        
        # 预处理图像
        pix = self._resize_and_pad_image(image, new_shape=int(image.shape[0] / self.stride) * self.stride)
        pix = np.transpose(pix, (2, 0, 1))  # CHW
        pix = np.expand_dims(pix, axis=0)  # BCHW
        pix = pix.astype(np.float32) / 255.0  # 归一化到[0, 1]
        new_h, new_w = pix.shape[2:]

        # 运行推理
        preds = self.session.run(None, {"images": pix})[0]

        # 后处理预测结果
        preds = preds[preds[..., 4] > 0.25]  # 置信度过滤
        preds[..., :4] = self._scale_boxes((new_h, new_w), preds[..., :4], (orig_h, orig_w))
        
        return YoloResult(boxes=preds, names=self.names)

    def detect(self, img):
        """
        检测图像中的页眉、页脚和左右边界
        Args:
            img: OpenCV格式的图像（BGR）
        Returns:
            tuple: (x_left, footer_top_y, x_right,header_bottom_y) 左右边界，页脚顶部，页眉底部
        """
        print(">>> 使用ONNX模型检测页眉页脚和左右边界...")
        height, width = img.shape[:2]

        # 执行推理
        result = self._inference(img)

        # 收集abandon框和文本框
        abandon_boxes = []
        text_boxes = []
        for box in result.boxes:
            class_id = int(box.cls)
            if result.names[class_id] == 'abandon':
                abandon_boxes.append(box.xyxy)
            elif result.names[class_id] in ['plain text', 'title']:
                text_boxes.append(box.xyxy)

        if not abandon_boxes:
            print("模型未检测到 'abandon' 区域，使用默认值。")
            header_bottom_y, footer_top_y = self._get_default_boundaries(height)
        else:
            # 计算上下边界
            header_bottom_y, footer_top_y = self._calculate_boundaries(abandon_boxes, height)

        # 计算左右边界
        if not text_boxes:
            print("未检测到文本区域，使用默认左右边界。")
            x_left, x_right = 0, width
        else:
            # 找到所有文本框的最左和最右边界
            x_left = min(float(box[0]) for box in text_boxes) - 3
            x_right = max(float(box[2]) for box in text_boxes) + 3
            
            # 确保边界在图像范围内
            x_left = max(0, x_left)
            x_right = min(width, x_right)
            print(f"检测到的左右边界: x_left={x_left}, x_right={x_right}")
        
        # 生成调试图像
        self._generate_debug_image(img, abandon_boxes, header_bottom_y, footer_top_y, x_left, x_right)

        return x_left, footer_top_y, x_right,header_bottom_y

    def _calculate_boundaries(self, abandon_boxes, height):
        """根据abandon框计算页眉页脚边界"""
        # 找到y坐标最小和最大的abandon框
        min_y_box = min(abandon_boxes, key=lambda b: b[1])
        max_y_box = max(abandon_boxes, key=lambda b: b[1])

        # y顶点最小的abandon + 2倍的abandon的h为页眉
        header_y1 = float(min_y_box[1])
        header_h = float(min_y_box[3] - min_y_box[1])
        header_bottom_y = int(header_y1 + 2 * header_h)

        # y顶点最大的abandon的y1 - 5像素为页脚
        footer_y1 = float(max_y_box[1])
        footer_top_y = int(footer_y1 - 5)

        # 确保边界在图像范围内
        header_bottom_y = max(0, min(header_bottom_y, height))
        footer_top_y = max(header_bottom_y, min(footer_top_y, height))

        print(f"检测到的边界: header_bottom_y={header_bottom_y}, footer_top_y={footer_top_y}")
        return header_bottom_y, footer_top_y

    def _get_default_boundaries(self, height):
        """获取默认的页眉页脚边界"""
        header_bottom_y = int(height * 0.1)
        footer_top_y = int(height * 0.95)
        return header_bottom_y, footer_top_y

    def _generate_debug_image(self, img, abandon_boxes, header_bottom_y, footer_top_y, x_left, x_right):
        """生成调试图像，显示检测结果"""
        debug_img = img.copy()
        height, width = img.shape[:2]

        # 用红线画出页眉和页脚
        cv2.line(debug_img, (0, header_bottom_y), (width, header_bottom_y), (0, 0, 255), 3)
        cv2.line(debug_img, (0, footer_top_y), (width, footer_top_y), (0, 0, 255), 3)

        # 用绿线画出左右边界
        cv2.line(debug_img, (int(x_left), 0), (int(x_left), height), (0, 255, 0), 3)
        cv2.line(debug_img, (int(x_right), 0), (int(x_right), height), (0, 255, 0), 3)

        # 用蓝色框标记所有检测到的abandon区域
        for box in abandon_boxes:
            x1, y1, x2, y2 = map(lambda x: int(float(x)), box)
            cv2.rectangle(debug_img, (x1, y1), (x2, y2), (255, 0, 0), 2)

        # 保存调试图像
        cv2.imwrite(os.path.join(self.debug_dir, 'ml_boundary_detection.png'), debug_img)

if __name__ == "__main__":
    # 测试代码
    detector = TextContentDetectorONNX(model_path="./model/doclayout_yolo_docstructbench_imgsz1024.onnx")
    
    # 读取测试图像
    test_image = cv2.imread("1.png")
    if test_image is not None:
        x_left,  footer_top_y, x_right,header_bottom_y = detector.detect(test_image)
        print(f"页眉底部：{header_bottom_y}，页脚顶部：{footer_top_y}")
        print(f"左右边界：x_left={x_left}, x_right={x_right}")
    else:
        print("无法读取测试图像") 