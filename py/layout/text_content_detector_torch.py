import cv2
import os
from doclayout_yolo import YOLOv10

class TextContentDetectorTorch:
    def __init__(self, model_path="./doclayout_yolo_docstructbench_imgsz1024.pt", debug_dir="output_data_cv/debug_images"):
        """
        初始化页眉页脚检测器
        Args:
            model_path: YOLOv10模型路径
            debug_dir: 调试图像输出目录
        """
        print(">>> 正在初始化 YOLOv10 引擎...")
        self.model = YOLOv10(model_path)
        self.debug_dir = debug_dir
        
        # 确保调试目录存在
        if not os.path.exists(debug_dir):
            os.makedirs(debug_dir)

    def detect(self, img):
        """
        检测图像中的页眉、页脚和左右边界
        Args:
            img: OpenCV格式的图像（BGR）
        Returns:
            tuple: (x_left, footer_top_y, x_right, header_bottom_y) 左右边界，页脚顶部，页眉底部
        """
        print(">>> 使用ML模型检测页眉页脚和左右边界...")
        height, width = img.shape[:2]

        # 执行预测
        det_res = self.model.predict(
            img,
            imgsz=1024,
            conf=0.2,
            device="cpu"
        )

        if not det_res:
            print("ML模型未返回任何检测结果，使用默认值。")
            header_bottom_y, footer_top_y = self._get_default_boundaries(height)
            return 0, footer_top_y, width, header_bottom_y

        result = det_res[0]
        abandon_boxes = self._collect_abandon_boxes(result)
        text_boxes = self._collect_text_boxes(result)

        if not abandon_boxes:
            print("ML模型未检测到 'abandon' 区域，使用默认值。")
            header_bottom_y, footer_top_y = self._get_default_boundaries(height)
        else:
            # 计算上下边界
            header_bottom_y, footer_top_y = self._calculate_boundaries(abandon_boxes, height)

        # 计算左右边界
        if not text_boxes:
            print("未检测到文本区域，使用默认左右边界。")
            x_left, x_right = 0, width
        else:
            # 找到所有文本框的最左和最右边界
            x_left = min(float(box[0]) for box in text_boxes) - 3
            x_right = max(float(box[2]) for box in text_boxes) + 3
            
            # 确保边界在图像范围内
            x_left = max(0, x_left)
            x_right = min(width, x_right)
            print(f"检测到的左右边界: x_left={x_left}, x_right={x_right}")
        
        # 生成调试图像
        self._generate_debug_image(img, abandon_boxes, header_bottom_y, footer_top_y, x_left, x_right)

        return x_left, footer_top_y, x_right, header_bottom_y

    def _collect_abandon_boxes(self, result):
        """收集所有分类为abandon的检测框"""
        abandon_boxes = []
        for box in result.boxes:
            class_id = int(box.cls)
            class_name = result.names[class_id]
            print(class_name)
            if class_name == 'abandon':
                abandon_boxes.append(box.xyxy.tolist()[0])  # [x1, y1, x2, y2]
        return abandon_boxes

    def _collect_text_boxes(self, result):
        """收集所有分类为文本的检测框"""
        text_boxes = []
        for box in result.boxes:
            class_id = int(box.cls)
            class_name = result.names[class_id]
            print(class_name)
            if class_name in ['plain text', 'title']:
                text_boxes.append(box.xyxy.tolist()[0])  # [x1, y1, x2, y2]
        return text_boxes

    def _calculate_boundaries(self, abandon_boxes, height):
        """根据abandon框计算页眉页脚边界"""
        # 找到y坐标最小和最大的abandon框
        min_y_box = min(abandon_boxes, key=lambda b: b[1])
        max_y_box = max(abandon_boxes, key=lambda b: b[1])

        # y顶点最小的abandon + 2倍的abandon的h为页眉
        header_y1 = min_y_box[1]
        header_h = min_y_box[3] - min_y_box[1]
        header_bottom_y = int(header_y1 + 2 * header_h)

        # y顶点最大的abandon的y1 - 5像素为页脚
        footer_y1 = max_y_box[1]
        footer_top_y = int(footer_y1 - 5)

        # 确保边界在图像范围内
        header_bottom_y = max(0, min(header_bottom_y, height))
        footer_top_y = max(header_bottom_y, min(footer_top_y, height))

        print(f"ML检测到的边界: header_bottom_y={header_bottom_y}, footer_top_y={footer_top_y}")
        return header_bottom_y, footer_top_y

    def _get_default_boundaries(self, height):
        """获取默认的页眉页脚边界"""
        header_bottom_y = int(height * 0.1)
        footer_top_y = int(height * 0.95)
        return header_bottom_y, footer_top_y

    def _generate_debug_image(self, img, abandon_boxes, header_bottom_y, footer_top_y, x_left, x_right):
        """生成调试图像，显示检测结果"""
        debug_img = img.copy()
        height, width = img.shape[:2]

        # 用红线画出页眉和页脚
        cv2.line(debug_img, (0, header_bottom_y), (width, header_bottom_y), (0, 0, 255), 3)
        cv2.line(debug_img, (0, footer_top_y), (width, footer_top_y), (0, 0, 255), 3)

        # 用红线画出左右边界
        cv2.line(debug_img, (int(x_left), 0), (int(x_left), height), (0, 0, 255), 3)
        cv2.line(debug_img, (int(x_right), 0), (int(x_right), height), (0, 0, 255), 3)

        # 用蓝色框标记所有检测到的abandon区域
        for box in abandon_boxes:
            x1, y1, x2, y2 = map(int, box)
            cv2.rectangle(debug_img, (x1, y1), (x2, y2), (255, 0, 0), 2)

        # 保存调试图像
        cv2.imwrite(os.path.join(self.debug_dir, 'ml_boundary_detection.png'), debug_img)

if __name__ == "__main__":
    # 测试代码
    detector = TextContentDetectorTorch()

    # 读取测试图像
    test_image = cv2.imread("1.png")
    if test_image is not None:
        x_left, footer_top_y, x_right, header_bottom_y = detector.detect(test_image)
        print(f"左边界：{x_left}，页脚顶部：{footer_top_y}，右边界：{x_right}，页眉底部：{header_bottom_y}")
    else:
        print("无法读取测试图像")