import cv2
import numpy as np
import os

def crop_margins(image, threshold_ratio=0.01):
    """使用垂直和水平投影，自动裁剪图像四周的空白或噪点边缘。"""
    if len(image.shape) == 3:
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
    else:
        gray = image.copy()

    _, binary = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY_INV + cv2.THRESH_OTSU)

    vertical_projection = np.sum(binary, axis=0)
    v_thresh = np.max(vertical_projection) * threshold_ratio

    x_left = 0
    for i, val in enumerate(vertical_projection):
        if val > v_thresh:
            x_left = i
            break

    x_right = len(vertical_projection) - 1
    for i in range(len(vertical_projection) - 1, -1, -1):
        if vertical_projection[i] > v_thresh:
            x_right = i
            break

    horizontal_projection = np.sum(binary, axis=1)
    h_thresh = np.max(horizontal_projection) * threshold_ratio

    y_top = 0
    for i, val in enumerate(horizontal_projection):
        if val > h_thresh:
            y_top = i
            break

    y_bottom = len(horizontal_projection) - 1
    for i in range(len(horizontal_projection) - 1, -1, -1):
        if horizontal_projection[i] > h_thresh:
            y_bottom = i
            break

    padding = 5
    x_left = max(0, x_left - padding)
    x_right = min(image.shape[1], x_right + padding)
    y_top = max(0, y_top - padding)
    y_bottom = min(image.shape[0], y_bottom + padding)

    return image[y_top:y_bottom, x_left:x_right]


def split_image_by_large_gaps(image_path,
                              output_dir='split_denoised_output',
                              median_ksize=3, # 中值滤波的核大小
                              min_gap_height=20,
                              blank_threshold=5):
    """
    在清洗过边缘的图像上，先进行中值滤波去噪，再通过寻找大间隙来分割词条。

    Args:
        image_path (str): 输入图像的路径。
        output_dir (str): 保存结果的文件夹。
        median_ksize (int): 中值滤波的核大小，必须是奇数，如3, 5, 7。
        min_gap_height (int): 定义一个“大间隙”的最小高度。
        blank_threshold (int): 定义空白行的像素和阈值。
    """
    # --- 1. 准备工作 ---
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)

    original_image = cv2.imread(image_path)
    if original_image is None:
        print(f"错误: 无法加载图像 '{image_path}'")
        return

    # --- 2. 边缘清洗 ---
    print(">>> 步骤 1: 裁剪图像边缘...")
    margin_cropped_image = crop_margins(original_image)
    cv2.imwrite(os.path.join(output_dir, "_1_margin_cropped.png"), margin_cropped_image)
    print("    -> 边缘已清洗。")

    # --- 3. 核心去噪步骤 ---
    print(">>> 步骤 2: 中值滤波去噪...")
    gray = cv2.cvtColor(margin_cropped_image, cv2.COLOR_BGR2GRAY)

    # 应用中值滤波
    # ksize必须是大于1的奇数
    denoised_gray = cv2.medianBlur(gray, median_ksize)
    cv2.imwrite(os.path.join(output_dir, "_2_denoised.png"), denoised_gray)
    print(f"    -> 已应用 {median_ksize}x{median_ksize} 中值滤波。")

    # --- 4. 在去噪后的图像上进行投影分析 ---
    print(">>> 步骤 3: 在去噪后的图像上寻找大间隙...")
    _, binary = cv2.threshold(denoised_gray, 0, 255, cv2.THRESH_BINARY_INV + cv2.THRESH_OTSU)
    horizontal_projection = np.sum(binary, axis=1)

    all_gaps = []
    current_gap_size = 0
    current_gap_start = -1
    for i, proj_val in enumerate(horizontal_projection):
        if proj_val <= blank_threshold:
            if current_gap_size == 0:
                current_gap_start = i
            current_gap_size += 1
        else:
            if current_gap_size > 0:
                all_gaps.append({'start': current_gap_start, 'size': current_gap_size})
            current_gap_size = 0
    if current_gap_size > 0:
        all_gaps.append({'start': current_gap_start, 'size': current_gap_size})

    large_gaps = [g for g in all_gaps if g['size'] >= min_gap_height]

    if not large_gaps:
        print("警告: 未找到高度超过阈值的大间隙，无法切割。")
        cv2.imwrite(os.path.join(output_dir, "entry_01_full_denoised.png"), denoised_gray)
        return

    print(f"    -> 找到 {len(large_gaps)} 个大间隙。")

    # --- 5. 执行切割与保存 ---
    # 切割时使用原始的、彩色的、仅裁剪过边缘的图像，以保留最佳质量
    cut_points = [g['start'] + g['size'] // 2 for g in large_gaps]
    cut_points.sort()
    print(f"    -> 将在以下 y 坐标处切割: {cut_points}")

    last_y = 0
    visual_image = margin_cropped_image.copy()

    for i, cut_y in enumerate(cut_points):
        part = margin_cropped_image[last_y:cut_y, :]
        filename = os.path.join(output_dir, f"entry_{i+1:02d}.png")
        cv2.imwrite(filename, part)
        print(f"        保存词条片段: {filename}")
        cv2.line(visual_image, (0, cut_y), (visual_image.shape[1], cut_y), (0, 255, 0), 2)
        last_y = cut_y

    final_part = margin_cropped_image[last_y:, :]
    final_filename = os.path.join(output_dir, f"entry_{len(cut_points)+1:02d}.png")
    cv2.imwrite(final_filename, final_part)
    print(f"        保存词条片段: {final_filename}")

    visual_filename = os.path.join(output_dir, "_3_visualized_cuts.png")
    cv2.imwrite(visual_filename, visual_image)
    print(f"    -> 已保存带切割线的可视化图。")


if __name__ == '__main__':
    INPUT_IMAGE_PATH = 'img.png' # <--- 请将这里换成你的图片文件名

    if not os.path.exists(INPUT_IMAGE_PATH):
        print(f"输入文件不存在: '{INPUT_IMAGE_PATH}'，请检查文件名或路径。")
    else:
        # 传入中值滤波的核大小，3通常是最好的起点
        split_image_by_large_gaps(INPUT_IMAGE_PATH, median_ksize=3)