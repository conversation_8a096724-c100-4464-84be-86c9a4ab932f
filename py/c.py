import cv2
import numpy as np
import os
import matplotlib.pyplot as plt

def split_image_at_largest_gap(image_path, output_dir='split_output', blank_threshold=5):
    """
    加载一张图片，找到其中最高的水平空白区域，并在此处将其一分为二。

    Args:
        image_path (str): 输入图像的路径。
        output_dir (str): 保存结果的文件夹。
        blank_threshold (int): 定义一个行为“空白”的像素和阈值。
                             对于纯黑白图像，可以为0。设置一个较小的值可以容忍噪点。
    """
    # --- 1. 准备工作 ---
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)

    original_image = cv2.imread(image_path)
    if original_image is None:
        print(f"错误: 无法加载图像 '{image_path}'")
        return

    # --- 2. 图像预处理和水平投影 ---
    gray = cv2.cvtColor(original_image, cv2.COLOR_BGR2GRAY)
    # 黑底白字，便于统计
    _, binary = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY_INV + cv2.THRESH_OTSU)

    # 计算水平投影
    horizontal_projection = np.sum(binary, axis=1)

    # --- 3. 寻找最大的连续空白区域 (核心逻辑) ---
    max_gap_size = 0
    max_gap_start = -1

    current_gap_size = 0
    current_gap_start = -1

    for i, proj_val in enumerate(horizontal_projection):
        # 如果当前行是空白行
        if proj_val <= blank_threshold:
            if current_gap_size == 0:
                current_gap_start = i  # 记录新空白区的开始
            current_gap_size += 1
        # 如果当前行有内容
        else:
            # 一个空白区刚刚结束，检查它是否是目前最大的
            if current_gap_size > max_gap_size:
                max_gap_size = current_gap_size
                max_gap_start = current_gap_start
            # 重置当前空白区计数器
            current_gap_size = 0
            current_gap_start = -1

    # 循环结束后，别忘了检查最后一个空白区
    if current_gap_size > max_gap_size:
        max_gap_size = current_gap_size
        max_gap_start = current_gap_start

    # --- 4. 计算切割点并执行切割 ---
    if max_gap_start == -1 or max_gap_size == 0:
        print("警告: 未在图像中找到明显的水平空白区域，无法切割。")
        return

    # 在最大空白区域的中间进行切割
    cut_y = max_gap_start + (max_gap_size // 2)

    print(f"找到最大空白区域: 起始行={max_gap_start}, 高度={max_gap_size}像素")
    print(f"将在 y = {cut_y} 处进行切割...")

    # 执行切割
    top_part = original_image[0:cut_y, :]
    bottom_part = original_image[cut_y:, :]

    # --- 5. 保存结果与可视化 ---
    # 构造文件名
    base_filename = os.path.splitext(os.path.basename(image_path))[0]
    top_filename = os.path.join(output_dir, f"{base_filename}_top.png")
    bottom_filename = os.path.join(output_dir, f"{base_filename}_bottom.png")
    visual_filename = os.path.join(output_dir, f"{base_filename}_visualized_cut.png")

    # 保存两个部分
    cv2.imwrite(top_filename, top_part)
    cv2.imwrite(bottom_filename, bottom_part)
    print(f"已保存上半部分: {top_filename}")
    print(f"已保存下半部分: {bottom_filename}")

    # 创建并保存可视化图像
    visual_image = original_image.copy()
    # 用一条亮绿色的粗线标出切割位置
    cv2.line(visual_image, (0, cut_y), (visual_image.shape[1], cut_y), (0, 255, 0), 3)
    cv2.imwrite(visual_filename, visual_image)
    print(f"已保存可视化切割图: {visual_filename}")


if __name__ == '__main__':
    # --- 配置 ---
    # 将要被切割的图片路径
    INPUT_IMAGE_PATH = 'dictionary_page_cropped.png' # 假设这是你已经切好页眉页脚的图片

    # 检查输入文件是否存在
    if not os.path.exists(INPUT_IMAGE_PATH):
        print(f"输入文件不存在: '{INPUT_IMAGE_PATH}'，请检查文件名或路径。")
    else:
        split_image_at_largest_gap(INPUT_IMAGE_PATH)