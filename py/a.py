import cv2
import numpy as np
import os
import json
import re
from paddleocr import PaddleOCR
# from header_footer_detector import HeaderFooterDetector

# --- 配置参数 (这些参数可能需要根据你的图片分辨率微调) ---
IMG_PATH = '1.png'
OUTPUT_DIR = 'output_data_cv'
IMG_OUTPUT_DIR = os.path.join(OUTPUT_DIR, 'output_images')
DEBUG_DIR = os.path.join(OUTPUT_DIR, 'debug_images')  # 新增调试图片目录
JSON_OUTPUT_PATH = os.path.join(OUTPUT_DIR, 'dictionary_data_cv.json')

# 轮廓筛选参数: 用于找到作为"锚点"的字头
HEADWORD_MIN_AREA = 1       # 字头轮廓的最小面积
HEADWORD_MAX_AREA = 2000      # 字头轮廓的最大面积
HEADWORD_ASPECT_RATIO_MIN = 0.8   # 字头轮廓的最小宽高比
HEADWORD_ASPECT_RATIO_MAX = 1.25  # 字头轮廓的最大宽高比
HEADWORD_MIN_SOLIDITY = 0.65    # 字头轮廓的最小密实度（轮廓面积/凸包面积）

# 添加垂直位置过滤
MIN_VERTICAL_DISTANCE = 100    # 字头之间的最小垂直距离

# 霍夫直线检测参数: 用于找到字形演变图中的横线
HOUGH_THRESHOLD = 50          # 累加器阈值
HOUGH_MIN_LINE_LENGTH = 50    # 线的最小长度
HOUGH_MAX_LINE_GAP = 10       # 线段之间允许的最大间隙

# 新增：锚点检测参数
ANCHOR_MIN_AREA = 3000        # 锚点的最小面积
ANCHOR_MAX_X_RATIO = 0.2      # 锚点的最大水平位置比例
ANCHOR_ASPECT_RATIO_MIN = 0.6  # 锚点的最小宽高比
ANCHOR_ASPECT_RATIO_MAX = 1.5  # 锚点的最大宽高比

# --- OCR 工具 ---
# 初始化一次，供后续重复使用
print(">>> 正在初始化 PaddleOCR 引擎...")
ocr_engine = PaddleOCR( use_doc_orientation_classify=False, # 通过 use_doc_orientation_classify 参数指定不使用文档方向分类模型
    use_doc_unwarping=False, # 通过 use_doc_unwarping 参数指定不使用文本图像矫正模型
    use_textline_orientation=False, # 通过 use_textline_orientation 参数指定不使用文本行方向分类模型
)

# # 初始化页眉页脚检测器
# header_footer_detector = HeaderFooterDetector(
#     model_path="./doclayout_yolo_docstructbench_imgsz1024.pt",
#     debug_dir=DEBUG_DIR
# )

def cut_entries(img):
    """
    使用进阶版的形态学操作来识别和切割每个词条。
    
    主要改进：
    1. 预处理时裁剪ROI，去除左侧装饰性边框（对于单栏图像，比例需要调整）
    2. 使用多步形态学操作（水平连接+垂直连接）
    3. 采用多条件智能筛选（面积 + 宽高比）
    
    Args:
        img (numpy.ndarray): 单栏的图像。
    
    Returns:
        list: 包含每个词条图像 (numpy.ndarray) 的列表。
    """
    print(">>> 步骤 1: 图像预处理与ROI裁剪...")
    
    # 1.1 裁剪ROI：去除左侧装饰性边框（单栏时比例调小）
    height, width = img.shape[:2]
    start_x = int(width * 0.02)  # 裁剪掉左侧2%（因为是单栏了，所以比例减小）
    roi_image = img[:, start_x:]
    cv2.imwrite(os.path.join(DEBUG_DIR, 'step1.1_roi.png'), roi_image)
    
    # 1.2 灰度化和二值化
    gray = cv2.cvtColor(roi_image, cv2.COLOR_BGR2GRAY)
    cv2.imwrite(os.path.join(DEBUG_DIR, 'step1.2_gray.png'), gray)

    # 使用自适应阈值，这样可以更好地处理不同区域的明暗变化
    binary = cv2.adaptiveThreshold(
        gray,
        255,
        cv2.ADAPTIVE_THRESH_GAUSSIAN_C,
        cv2.THRESH_BINARY_INV,
        blockSize=15,  # 必须是奇数
        C=2
    )
    cv2.imwrite(os.path.join(DEBUG_DIR, 'step1.3_binary.png'), binary)

    print(">>> 步骤 2: 多步形态学操作...")
    
    # 2.1 首先进行一次小规模闭运算，填充字符内的小空隙
    close_kernel_small = cv2.getStructuringElement(cv2.MORPH_RECT, (3, 3))
    closed_small = cv2.morphologyEx(binary, cv2.MORPH_CLOSE, close_kernel_small)
    cv2.imwrite(os.path.join(DEBUG_DIR, 'step2.1_closed_small.png'), closed_small)
    
    # 2.2 水平方向的连接：使用"矮胖"的核（单栏时核的宽度减小）
    kernel_horizontal = cv2.getStructuringElement(cv2.MORPH_RECT, (15, 3))  # 宽度从25减到15
    connected_horizontal = cv2.dilate(closed_small, kernel_horizontal, iterations=2)
    cv2.imwrite(os.path.join(DEBUG_DIR, 'step2.2_connected_horizontal.png'), connected_horizontal)
    
    # 2.3 垂直方向的连接：使用较小的"瘦高"核
    kernel_vertical = cv2.getStructuringElement(cv2.MORPH_RECT, (3, 7))
    connected_vertical = cv2.dilate(connected_horizontal, kernel_vertical, iterations=2)
    cv2.imwrite(os.path.join(DEBUG_DIR, 'step2.3_connected_vertical.png'), connected_vertical)
    
    # 2.4 最后进行一次适度的闭运算（单栏时核的宽度减小）
    estimated_entry_height = 10
    close_kernel_large = cv2.getStructuringElement(cv2.MORPH_RECT, (10, estimated_entry_height))  # 宽度从20减到10
    final_closed = cv2.morphologyEx(connected_vertical, cv2.MORPH_CLOSE, close_kernel_large)
    
    # 2.5 可选的最终清理：去除小噪点
    kernel_clean = cv2.getStructuringElement(cv2.MORPH_RECT, (3, 3))
    final_cleaned = cv2.morphologyEx(final_closed, cv2.MORPH_OPEN, kernel_clean)
    cv2.imwrite(os.path.join(DEBUG_DIR, 'step2.5_final_cleaned.png'), final_cleaned)

    print(">>> 步骤 3: 轮廓查找与智能筛选...")
    
    # 3.1 查找轮廓
    contours, _ = cv2.findContours(final_cleaned, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    print(f"找到 {len(contours)} 个初始轮廓")
    
    # 3.2 智能筛选：结合多个条件（单栏时面积阈值需要调整）
    valid_contours = []
    min_area = int(height * width * 0.002)  # 减小最小面积阈值（因为是单栏）
    for contour in contours:
        # 计算面积
        area = cv2.contourArea(contour)
        if area < min_area:
            continue
            
        # 获取边界框并计算宽高比
        x, y, w, h = cv2.boundingRect(contour)
        aspect_ratio = w / float(h)
        
        # 词条通常是扁平的长方形，设定合理的宽高比范围（单栏时范围调整）
        if not (1.5 < aspect_ratio < 6.0):  # 调整宽高比范围
            continue
            
        valid_contours.append((x, y, w, h))
    
    print(f"筛选后剩余 {len(valid_contours)} 个有效轮廓")
    
    # 3.3 按垂直位置排序
    valid_contours.sort(key=lambda box: box[1])
    
    # 在调试图像上绘制最终确定的边界框
    debug_img = roi_image.copy()
    for i, (x, y, w, h) in enumerate(valid_contours):
        color = (0, 255, 0)  # 绿色
        cv2.rectangle(debug_img, (x, y), (x + w, y + h), color, 3)
        cv2.putText(debug_img, str(i + 1), (x, y - 10), cv2.FONT_HERSHEY_SIMPLEX, 1.2, color, 3)
    cv2.imwrite(os.path.join(DEBUG_DIR, 'step3_final_boxes.png'), debug_img)

    print(">>> 步骤 4: 裁剪并收集词条图像...")
    # 4. 裁剪：根据边界框从ROI图像中裁剪出词条
    all_entries = []
    for x, y, w, h in valid_contours:
        # 在ROI图像上进行切片
        cropped_entry = roi_image[y:y+h, x:x+w]
        all_entries.append(cropped_entry)
    
    return all_entries

def parse_entry_text(text):
    """
    使用正则表达式解析一个词条的完整文本，提取关键信息。
    """
    # 清理文本，将多余的换行符合并
    text = re.sub(r'\s+', ' ', text).strip()
    
    # \s*(\S)\s* 匹配第一个字（字头）
    # (?:\((.*?)\))? 匹配可选的繁体
    # \s*([a-zāáǎàēéěèīíǐìōóǒòūúǔùǖǘǚǜü]+\d?) 匹配拼音
    pattern = re.compile(r"(\S)\s*(?:\((.*?)\))?\s*([a-zāáǎàēéěèīíǐìōóǒòūúǔùǖǘǚǜü]+\d?)\s*(.*)", re.DOTALL)
    match = pattern.match(text)
    
    if match:
        return {
            "character": match.group(1),
            "other_forms": match.group(2) or "",
            "pinyin": match.group(3),
            "explanation": match.group(4).strip()
        }
    # 如果正则不匹配，返回原始文本作为解释，以防丢失信息
    return {"explanation": text}

def separate_text_and_figure(entry_img):
    """
    在一个词条图像块中，分离出文字区域和字形演变图区域。
    Args:
        entry_img (numpy.ndarray): 单个词条的图像（灰度图）。
    Returns:
        tuple: (text_only_img, figure_img)
               figure_img 可能是 None。
    """
    # 寻找横线来定位图片
    # 使用Canny边缘检测，为霍夫变换做准备
    edges = cv2.Canny(entry_img, 50, 150, apertureSize=3)
    lines = cv2.HoughLinesP(edges, 1, np.pi / 180, HOUGH_THRESHOLD,
                            minLineLength=HOUGH_MIN_LINE_LENGTH,
                            maxLineGap=HOUGH_MAX_LINE_GAP)

    if lines is None:
        # 没有检测到线，认为整个块都是文本
        return entry_img, None

    # 找到所有横线覆盖的垂直范围
    min_y, max_y = entry_img.shape[0], 0
    line_found = False
    for line in lines:
        x1, y1, x2, y2 = line[0]
        # 只关心近似水平的线
        if abs(y1 - y2) < 10:
            line_found = True
            min_y = min(min_y, y1, y2)
            max_y = max(max_y, y1, y2)
            
    if not line_found:
        return entry_img, None

    # 提取图片区域，并上下加一点边距
    padding = 5
    figure_region = (max(0, min_y - padding), min(entry_img.shape[0], max_y + padding))
    
    figure_img = entry_img[figure_region[0]:figure_region[1], :]
    
    # 从原始词条块中"抹掉"图片区域，得到纯文本图
    text_only_img = entry_img.copy()
    cv2.rectangle(text_only_img, (0, figure_region[0]), (text_only_img.shape[1], figure_region[1]), (0, 0, 0), -1)

    return text_only_img, figure_img

# def find_header_footer_ml(img, model):
#     """
#     使用ML模型检测页眉和页脚。
#     1. 运行模型预测
#     2. 收集分类为 'abandon' 的检测结果
#     3. 根据 'abandon' 结果计算页眉和页脚边界
#     4. 绘制边界并保存调试图像
#     """
#     print(">>> 使用ML模型检测页眉页脚...")
#     height, width = img.shape[:2]
#
#     # Perform prediction
#     det_res = model.predict(
#         img,
#         imgsz=1024,
#         conf=0.2,
#         device="cpu"
#     )
#
#     if not det_res:
#         print("ML模型未返回任何检测结果，使用默认值。")
#         header_bottom_y = int(height * 0.1)
#         footer_top_y = int(height * 0.95)
#         return header_bottom_y, footer_top_y
#
#     result = det_res[0]
#
#     # 1. 收集分类为abandon
#     abandon_boxes = []
#     for box in result.boxes:
#         class_id = int(box.cls)
#         class_name = result.names[class_id]
#         if class_name == 'abandon':
#             abandon_boxes.append(box.xyxy.tolist()[0])  # [x1, y1, x2, y2]
#
#     if not abandon_boxes:
#         print("ML模型未检测到 'abandon' 区域，使用默认值。")
#         header_bottom_y = int(height * 0.1)
#         footer_top_y = int(height * 0.95)
#         return header_bottom_y, footer_top_y
#
#     # 找到y坐标最小和最大的 'abandon' 框
#     min_y_box = min(abandon_boxes, key=lambda b: b[1])
#     max_y_box = max(abandon_boxes, key=lambda b: b[1])
#
#     # 2. y 顶点最小的 abandon + 2倍的 abandon的 h 为页眉
#     header_y1 = min_y_box[1]
#     header_h = min_y_box[3] - min_y_box[1]
#     header_bottom_y = int(header_y1 + 2 * header_h)
#
#     # 3. y 顶点最大的 abandon 的y1 - 5 像素为页脚
#     footer_y1 = max_y_box[1]
#     footer_top_y = int(footer_y1 - 5)
#
#     # 确保边界在图像范围内
#     header_bottom_y = max(0, min(header_bottom_y, height))
#     footer_top_y = max(header_bottom_y, min(footer_top_y, height))
#
#     print(f"ML检测到的边界: header_bottom_y={header_bottom_y}, footer_top_y={footer_top_y}")
#
#     # 4. 用红线在图中画出，保存调试文件
#     debug_img = img.copy()
#     # 绘制页眉和页脚线
#     cv2.line(debug_img, (0, header_bottom_y), (width, header_bottom_y), (0, 0, 255), 3)
#     cv2.line(debug_img, (0, footer_top_y), (width, footer_top_y), (0, 0, 255), 3)
#
#     # 同时用蓝色框画出所有检测到的 'abandon' 区域，方便调试
#     for box in abandon_boxes:
#         x1, y1, x2, y2 = map(int, box)
#         cv2.rectangle(debug_img, (x1, y1), (x2, y2), (255, 0, 0), 2)
#
#     cv2.imwrite(os.path.join(DEBUG_DIR, 'ml_boundary_detection.png'), debug_img)
#
#     return header_bottom_y, footer_top_y

def split_columns(img):
    """
    将图片分为左右两栏
    返回：(left_column, right_column, split_x)
    """
    gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
    _, binary = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY_INV + cv2.THRESH_OTSU)
    
    # 计算垂直投影
    vertical_projection = np.sum(binary, axis=0)
    
    # 在页面中间区域找到投影的最小值（栏间距）
    center_region_start = img.shape[1] // 3
    center_region_end = img.shape[1] * 2 // 3
    split_x = center_region_start + np.argmin(vertical_projection[center_region_start:center_region_end])
    
    left_column = img[:, :split_x]
    right_column = img[:, split_x:]
    
    return left_column, right_column, split_x

def process_columns_connection(left_column, right_column, left_entries, right_entries):
    """
    处理跨栏的文本
    
    Args:
        left_column (np.ndarray): 左栏的完整图像
        right_column (np.ndarray): 右栏的完整图像
        left_entries (list): 左栏中检测到的词条图像列表
        right_entries (list): 右栏中检测到的词条图像列表
    
    Returns:
        tuple: (left_entries, right_entries) 处理后的左右栏词条列表
    """
    if not right_entries:
        return left_entries, right_entries

    # 获取右栏第一个词条的高度
    first_right_entry_height = right_entries[0].shape[0]
    
    # 如果右栏第一个词条的高度明显大于普通词条（说明可能包含了跨栏内容）
    if first_right_entry_height > 100:  # 假设普通词条高度小于100像素
        # 将右栏第一个词条分成两部分
        split_point = first_right_entry_height // 2  # 简单地从中间分割
        
        if left_entries:
            # 将上半部分添加到左栏最后一个词条
            top_content = right_entries[0][:split_point, :]
            last_left_entry = left_entries[-1]
            
            # 调整图像大小使其宽度匹配
            if top_content.shape[1] != last_left_entry.shape[1]:
                scale = last_left_entry.shape[1] / top_content.shape[1]
                new_height = int(top_content.shape[0] * scale)
                top_content = cv2.resize(top_content, (last_left_entry.shape[1], new_height))
            
            # 垂直拼接
            extended_entry = np.vstack([last_left_entry, top_content])
            left_entries[-1] = extended_entry
        
        # 更新右栏第一个词条为下半部分
        right_entries[0] = right_entries[0][split_point:, :]
    
    return left_entries, right_entries

def find_headword_candidates(column_img):
    """在单栏中寻找所有可能的字头候选轮廓。"""
    gray = cv2.cvtColor(column_img, cv2.COLOR_BGR2GRAY)
    _, binary = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY_INV + cv2.THRESH_OTSU)
    contours, _ = cv2.findContours(binary, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    
    candidates = []
    # 我们只在栏的左侧部分寻找字头
    headword_search_region_width = int(column_img.shape[1] * 0.25)
    
    for cnt in contours:
        x, y, w, h = cv2.boundingRect(cnt)
        
        # 过滤掉不在指定区域的轮廓
        if x + w // 2 > headword_search_region_width:
            continue
            
        area = cv2.contourArea(cnt)
        if not (HEADWORD_MIN_AREA < area < HEADWORD_MAX_AREA):
            continue
            
        aspect_ratio = w / float(h)
        if not (HEADWORD_ASPECT_RATIO_MIN < aspect_ratio < HEADWORD_ASPECT_RATIO_MAX):
            continue
            
        hull = cv2.convexHull(cnt)
        hull_area = cv2.contourArea(hull)
        if hull_area > 0:
            solidity = area / hull_area
            if solidity < HEADWORD_MIN_SOLIDITY:
                continue
        else:
            continue
            
        candidates.append((x, y, w, h))
        
    return candidates

def filter_and_sort_headwords(candidates):
    """从候选者中筛选出最终的字头，并按垂直位置排序。"""
    if not candidates:
        return []

    # 按 y 坐标排序，方便我们进行垂直距离过滤
    candidates.sort(key=lambda c: c[1])

    # 垂直过滤：如果两个字头靠得太近，我们可能需要根据某些规则选择一个
    # 这里我们采用一个简单的策略：只保留第一个，并跳过后面所有在最小距离之内的
    final_headwords = []
    last_y = -float('inf')
    
    for (x, y, w, h) in candidates:
        if y > last_y + MIN_VERTICAL_DISTANCE:
            final_headwords.append((x, y, w, h))
            last_y = y
            
    return final_headwords

def find_headword_anchors(img):
    """
    在图像中找到所有作为"锚点"的字头。
    
    Args:
        img (np.ndarray): 输入图像
        
    Returns:
        list: 包含所有锚点轮廓信息的列表，每个元素是 (x, y, w, h) 格式
    """
    print(">>> 步骤 1: 寻找字头锚点...")
    
    # 1. 基础预处理
    gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
    _, binary = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY_INV + cv2.THRESH_OTSU)
    cv2.imwrite(os.path.join(DEBUG_DIR, 'step1.1_binary.png'), binary)
    
    # 2. 查找初始轮廓（不进行形态学操作）
    contours, _ = cv2.findContours(binary, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    print(f"找到 {len(contours)} 个初始轮廓")
    
    # 3. 智能筛选找出"锚点"
    anchors = []
    height, width = img.shape[:2]
    max_x = int(width * ANCHOR_MAX_X_RATIO)  # 最大允许的x坐标
    
    for cnt in contours:
        # a. 面积筛选
        area = cv2.contourArea(cnt)
        if area < ANCHOR_MIN_AREA:
            continue
            
        # b. 位置筛选
        x, y, w, h = cv2.boundingRect(cnt)
        if x > max_x:
            continue
            
        # c. 宽高比筛选
        aspect_ratio = w / float(h)
        if not (ANCHOR_ASPECT_RATIO_MIN < aspect_ratio < ANCHOR_ASPECT_RATIO_MAX):
            continue
            
        anchors.append((x, y, w, h))
    
    # 4. 按y坐标排序
    anchors.sort(key=lambda box: box[1])
    
    print(f"找到 {len(anchors)} 个有效锚点")
    
    # 5. 在调试图像上绘制找到的锚点
    debug_img = img.copy()
    for i, (x, y, w, h) in enumerate(anchors):
        cv2.rectangle(debug_img, (x, y), (x + w, y + h), (0, 255, 0), 2)
        cv2.putText(debug_img, f"A{i+1}", (x, y - 5), cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 255, 0), 2)
    cv2.imwrite(os.path.join(DEBUG_DIR, 'step1.2_anchors.png'), debug_img)
    
    return anchors

def define_search_zones(img, anchors):
    """
    根据锚点定义垂直搜索区域。
    
    Args:
        img (np.ndarray): 输入图像
        anchors (list): 锚点列表，每个元素是 (x, y, w, h) 格式
        
    Returns:
        list: 搜索区域列表，每个元素是 (y_start, y_end) 格式
    """
    print(">>> 步骤 2: 定义搜索区域...")
    
    height = img.shape[0]
    zones = []
    
    for i in range(len(anchors)):
        # 当前锚点的y坐标
        y_start = anchors[i][1]
        
        # 如果是最后一个锚点，y_end就是图片底部
        if i == len(anchors) - 1:
            y_end = height
        else:
            # 否则，y_end是下一个锚点的y坐标
            y_end = anchors[i + 1][1]
        
        zones.append((y_start, y_end))
    
    # 在调试图像上绘制搜索区域
    debug_img = img.copy()
    for i, (y_start, y_end) in enumerate(zones):
        cv2.line(debug_img, (0, y_start), (img.shape[1], y_start), (0, 0, 255), 2)
        cv2.line(debug_img, (0, y_end), (img.shape[1], y_end), (0, 255, 0), 2)
        cv2.putText(debug_img, f"Zone {i+1}", (10, (y_start + y_end) // 2), 
                    cv2.FONT_HERSHEY_SIMPLEX, 0.8, (255, 0, 0), 2)
    cv2.imwrite(os.path.join(DEBUG_DIR, 'step2_zones.png'), debug_img)
    
    return zones

def extract_entry_from_zone(img, zone):
    """
    从搜索区域中提取词条。
    
    Args:
        img (np.ndarray): 输入图像
        zone (tuple): 搜索区域的 (y_start, y_end)
        
    Returns:
        np.ndarray: 提取出的词条图像
    """
    y_start, y_end = zone
    
    # 1. 裁剪搜索区域
    zone_img = img[y_start:y_end, :]
    
    # 2. 预处理
    gray = cv2.cvtColor(zone_img, cv2.COLOR_BGR2GRAY)
    _, binary = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY_INV + cv2.THRESH_OTSU)
    
    # 3. 形态学闭操作，合并区域内的所有内容
    kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (50, 50))
    closed = cv2.morphologyEx(binary, cv2.MORPH_CLOSE, kernel)
    
    # 4. 查找最大轮廓
    contours, _ = cv2.findContours(closed, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    if not contours:
        return None
        
    # 找到最大的轮廓
    max_cnt = max(contours, key=cv2.contourArea)
    x, y, w, h = cv2.boundingRect(max_cnt)
    
    # 5. 从原始区域图像中裁剪出词条
    entry_img = zone_img[y:y+h, x:x+w]
    
    return entry_img

def cut_entries_with_anchors(img):
    """
    使用基于锚点的策略切割词条。
    
    Args:
        img (np.ndarray): 输入图像
        
    Returns:
        list: 包含所有词条图像的列表
    """
    # 1. 找到所有字头锚点
    anchors = find_headword_anchors(img)
    
    # 2. 定义搜索区域
    zones = define_search_zones(img, anchors)
    
    # 3. 从每个区域中提取词条
    entries = []
    for i, zone in enumerate(zones):
        entry = extract_entry_from_zone(img, zone)
        if entry is not None:
            entries.append(entry)
            # 保存调试图像
            cv2.imwrite(os.path.join(DEBUG_DIR, f'entry_{i+1}.png'), entry)
    
    return entries

def main():
    """主执行函数"""
    print(">>> 开始处理字典页面...")
    
    # 1. 准备工作：创建输出目录
    if not os.path.exists(OUTPUT_DIR):
        os.makedirs(OUTPUT_DIR)
    if not os.path.exists(IMG_OUTPUT_DIR):
        os.makedirs(IMG_OUTPUT_DIR)
    if not os.path.exists(DEBUG_DIR):
        os.makedirs(DEBUG_DIR)
        
    # 2. 读取图片
    img = cv2.imread(IMG_PATH)
    if img is None:
        raise ValueError(f"无法读取图片：{IMG_PATH}")
    
    # 3. 使用 ML 模型检测并移除页眉页脚
    header_bottom_y, footer_top_y = 82,1493# header_footer_detector.detect(img)
    main_content = img[header_bottom_y:footer_top_y, :]
    
    # 保存去除页眉页脚后的图片用于调试
    cv2.imwrite(os.path.join(DEBUG_DIR, 'main_content_no_header_footer.png'), main_content)

    # 4. 分栏处理
    print(">>> 步骤 4: 分栏处理...")
    left_column, right_column, split_x = split_columns(main_content)
    
    # 保存分栏结果用于调试
    cv2.imwrite(os.path.join(DEBUG_DIR, 'left_column.png'), left_column)
    cv2.imwrite(os.path.join(DEBUG_DIR, 'right_column.png'), right_column)
    
    # 在原图上画出分栏线，用于调试
    split_line_img = main_content.copy()
    cv2.line(split_line_img, (split_x, 0), (split_x, split_line_img.shape[0]), (0, 0, 255), 2)
    cv2.imwrite(os.path.join(DEBUG_DIR, 'split_line.png'), split_line_img)

    # 5. 使用基于锚点的方法处理左右栏
    print(">>> 步骤 5: 处理左栏...")
    left_entries = cut_entries_with_anchors(left_column)
    
    print(">>> 步骤 6: 处理右栏...")
    right_entries = cut_entries_with_anchors(right_column)
    
    # 7. 合并左右栏的词条
    all_entries = left_entries + right_entries

    # 8. 保存所有处理好的词条图片
    print(">>> 步骤 8: 保存词条图片...")
    for i, entry_img in enumerate(all_entries):
        entry_filename = f'entry_{i+1:03d}.png'
        cv2.imwrite(os.path.join(IMG_OUTPUT_DIR, entry_filename), entry_img)

    print("处理完成！")
    print(f"总共找到 {len(all_entries)} 个词条（左栏：{len(left_entries)}，右栏：{len(right_entries)}）。")
    print(f"图片已保存至目录：{IMG_OUTPUT_DIR}")
    print(f"调试图片已保存至目录：{DEBUG_DIR}")

if __name__ == '__main__':
    main()