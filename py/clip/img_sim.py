import argparse
from typing import List
import onnxruntime
from PIL import Image
import numpy as np

def _convert_to_rgb(image):
    return image.convert('RGB')

def image_transform(image_size=224):
    def transform(image):
        image = _convert_to_rgb(image)
        image = image.resize((image_size, image_size), Image.BICUBIC)
        image = np.array(image, dtype=np.float32) / 255.0
        mean = np.array([0.48145466, 0.4578275, 0.40821073], dtype=np.float32)
        std = np.array([0.26862954, 0.26130258, 0.27577711], dtype=np.float32)
        image = (image - mean) / std
        image = np.transpose(image, (2, 0, 1))
        image = np.expand_dims(image, axis=0)
        return image.astype(np.float32)
    return transform

def load_clip_image_model(model_path: str):
    """加载CLIP图像模型"""
    sess_options = onnxruntime.SessionOptions()
    sess_options.log_severity_level = 2
    run_options = onnxruntime.RunOptions()
    run_options.log_severity_level = 2
    
    session = onnxruntime.InferenceSession(
        model_path,
        sess_options=sess_options
    )
    return session

def extract_image_features(image_path: str, model, preprocess) -> np.ndarray:
    """提取单张图片的特征向量"""
    image = preprocess(Image.open(image_path))
    
    # 核心修改：將輸出的節點名稱從 "unnorm_image_features" 改為 "image_features"
    # 這個節點輸出的是經過 L2 歸一化的標準 CLIP 特徵
    features = model.run(["unnorm_image_features"], {"image": image})[0]
    
    # 因為 "image_features" 節點的輸出已經是歸一化的，所以函式內不再需要手動歸一化
    features = features / np.linalg.norm(features, axis=-1, keepdims=True) # <--- 刪除或註解掉此行
    
    return features



def calculate_similarity(vec1: np.ndarray, vec2: np.ndarray) -> float:
    """计算两个向量的余弦相似度"""
    # 由于输入向量已经归一化，直接计算点积即可得到余弦相似度
    similarity = float(np.dot(vec1, vec2.T))
    return similarity

def main():
    parser = argparse.ArgumentParser(description='计算多张图片与基准图片的相似度')
    parser.add_argument('images', nargs='+', help='图片路径列表，第一张为基准图片')
    parser.add_argument('--model', default='/Users/<USER>/work/hanzi/py/model/clip/vit-b-16.img.fp16.onnx',
                      help='CLIP图像模型路径')
    args = parser.parse_args()

    if len(args.images) < 2:
        print("错误：至少需要输入两张图片")
        return

    # 加载模型
    print(f"正在加载模型: {args.model}")
    model = load_clip_image_model(args.model)
    preprocess = image_transform(224)  # 使用ViT-B-16的默认分辨率

    # 提取基准图片特征
    base_image = args.images[0]
    print(f"\n处理基准图片: {base_image}")
    base_features = extract_image_features(base_image, model, preprocess)
    print(f"基准图片特征向量维度: {base_features.shape}")
    print(f"基准图片特征向量前10维: {base_features[0][:10]}")

    # 处理其他图片
    print("\n计算相似度:")
    for img_path in args.images[1:]:
        print(f"\n处理图片: {img_path}")
        features = extract_image_features(img_path, model, preprocess)
        similarity = calculate_similarity(base_features[0], features[0])
        print(f"与基准图片的相似度: {similarity:.4f}")

if __name__ == "__main__":
    main() 