好的，我们来分析一下使用 Go、`gonum` 和 `onnxruntime_go` 重构这个 Python 脚本的可行性，并制定一个详细的方案。

### 可行性分析

**结论：完全可行，并且是一个很好的选择。**

1.  **可行性**:
    * **ONNX 推理**: `yalue/onnxruntime_go` 是对官方 ONNX Runtime C API 的 Go 语言封装 (wrapper)，功能强大且性能良好。它可以加载你的 `.onnx` 模型文件，并执行推理，这与 Python 中的 `onnxruntime` 库功能对等。
    * **数值计算**: `gonum` 是 Go 语言生态中最成熟、功能最完备的科学计算和矩阵运算库，可以完美对标 Python 中的 `numpy`。所有在 Python 脚本中看到的向量/矩阵操作（归一化、内积、转置、softmax 等）都可以在 `gonum` 中找到对应实现。
    * **图像处理**: Go 的标准库 `image` 和扩展库 `golang.org/x/image/draw` 提供了强大的图像解码、格式转换和缩放功能，可以替代 Python 的 `Pillow (PIL)`。
    * **文本分词**: 这是**最复杂的部分**。`bert_tokenizer.py` 实现了一套完整的 BERT 分词逻辑，包括对 CJK 字符的处理、标点分割和 WordPiece 算法。这部分没有现成的、与你的脚本 100% 匹配的 Go 库，需要手动移植。

2.  **替代方案**:
    * **方案一（推荐，本次方案基于此）**: **用 Go 完全重构**。这是最“纯粹”的方案，最终会得到一个不依赖 Python 环境、性能高、易于部署的独立二进制文件。挑战在于分词器的移植工作量。
    * **方案二（不推荐，但可行）**: **Go + gRPC/HTTP 服务**。将分词器 `bert_tokenizer.py` 封装成一个独立的 Python 微服务（使用 Flask 或 gRPC），Go 程序通过网络调用它来获取分词结果。这可以绕过最复杂的移植工作，但引入了服务间通信的复杂性，降低了整体性能和部署的便捷性。

我们将按照推荐的**方案一**来制定详细的步骤和关键代码。

---

### 可行性方案与步骤指导

我们的目标是创建一个 Go 程序，它能独立完成 Python 脚本中的所有任务。

#### 项目结构与依赖

首先，初始化你的 Go 项目并获取必要的库：

```sh
go mod init my-clip-app
go get gonum.org/v1/gonum
go get github.com/yalue/onnxruntime_go
go get golang.org/x/image/draw
```

#### 第一步：移植分词器 (`bert_tokenizer.py`)

这是整个重构工作的核心和难点。你需要创建一个 `tokenizer` Go 包，将 `bert_tokenizer.py` 的逻辑翻译过来。

**1. 加载词汇表 (`load_vocab`)**
- 读取 `vocab.txt` 文件。
- 创建一个 `map[string]int32` 来存储 `token -> id` 的映射，以及一个 `map[int32]string` 用于反向查找。

**2. 实现 `BasicTokenizer`**
- **`_clean_text`**: 遍历字符串，移除无效字符，将空白符合并为空格。Go 的 `unicode` 包可以检查字符类别。
- **`_is_chinese_char`**: `_is_chinese_char` 中的 Unicode 码点范围判断可以直接翻译成 Go 的 `if` 条件。
- **`_tokenize_chinese_chars`**: 遍历字符串，如果 `_is_chinese_char` 返回 `true`，则在字符两边添加空格。
- **`_run_split_on_punc`**: 遍历字符，使用 `unicode.IsPunct` 或类似逻辑来分割标点。
- **`_run_strip_accents`**: 这需要使用 `golang.org/x/text/unicode/norm` 包来对 Unicode 进行规范化，以移除重音符号。

**3. 实现 `WordpieceTokenizer`**
- 这是最核心的算法。它使用贪心、最长匹配优先的策略。
- 你需要在一个循环中，从词的末尾开始向前递减，构建子字符串（对于非词首的子串，要在前面加上 `##`），然后在词汇表 `map` 中检查是否存在。
- 如果找到，就记录这个子词，并从下一个位置继续处理剩余部分。
- 如果一个词无法被完全切分，则替换为 `[UNK]`。

**关键代码框架 (tokenizer/tokenizer.go):**

```go
package tokenizer

import (
	"bufio"
	"os"
	"strings"
	"unicode"

	"golang.org/x/text/unicode/norm" // 用于处理重音
)

// FullTokenizer 结构体，持有所有必要的数据
type FullTokenizer struct {
	Vocab     map[string]int32
	InvVocab  map[int32]string
	// BasicTokenizer 和 WordpieceTokenizer 也可以是结构体
}

// NewFullTokenizer 是构造函数
func NewFullTokenizer(vocabFile string, doLowerCase bool) (*FullTokenizer, error) {
	// 1. 加载 vocab.txt
	vocab, err := loadVocab(vocabFile)
	if err != nil {
		return nil, err
	}

	// 2. 创建反向词汇表
	invVocab := make(map[int32]string, len(vocab))
	for k, v := range vocab {
		invVocab[v] = k
	}
	
	// ... 初始化其他部分
	return &FullTokenizer{Vocab: vocab, InvVocab: invVocab}, nil
}

// Tokenize 实现完整的文本分词逻辑
func (t *FullTokenizer) Tokenize(text string) []string {
	// 1. 调用 BasicTokenizer 的逻辑
	basicTokens := t.basicTokenize(text)
	
	// 2. 对每个 basic token 调用 WordpieceTokenizer 的逻辑
	var wordpieceTokens []string
	for _, token := range basicTokens {
		wordpieceTokens = append(wordpieceTokens, t.wordpieceTokenize(token)...)
	}
	return wordpieceTokens
}

// ConvertTokensToIDs 将 token 字符串数组转换为 ID 数组
func (t *FullTokenizer) ConvertTokensToIDs(tokens []string) []int32 {
	ids := make([]int32, len(tokens))
	for i, token := range tokens {
		ids[i] = t.Vocab[token]
	}
	return ids
}

// ... basicTokenize 和 wordpieceTokenize 的具体实现 ...
```

#### 第二步：实现图像预处理 (`img.py`)

这部分用 Go 标准库和 `gonum` 来实现。

**关键代码框架 (main.go):**
```go
import (
	"image"
	_ "image/jpeg" // 注册jpeg解码器
	"os"

	"gonum.org/v1/gonum/mat"
	"golang.org/x/image/draw"
)

// ImageTransform 对应 Python 中的 image_transform 函数
func ImageTransform(imgPath string, targetSize int) (*mat.Dense, error) {
	file, err := os.Open(imgPath)
	if err != nil {
		return nil, err
	}
	defer file.Close()

	img, _, err := image.Decode(file)
	if err != nil {
		return nil, err
	}

	// 1. 调整大小 (BICUBIC 需要 draw.CatmullRom，效果类似)
	dst := image.NewRGBA(image.Rect(0, 0, targetSize, targetSize))
	draw.CatmullRom.Scale(dst, dst.Bounds(), img, img.Bounds(), draw.Over, nil)

	// 2. 转换为 (C, H, W) 的浮点数矩阵并进行标准化
	// H=targetSize, W=targetSize, C=3
	chwData := make([]float64, 3*targetSize*targetSize)
	mean := []float64{0.48145466, 0.4578275, 0.40821073}
	std := []float64{0.26862954, 0.26130258, 0.27577711}

	for y := 0; y < targetSize; y++ {
		for x := 0; x < targetSize; x++ {
			r, g, b, _ := dst.At(x, y).RGBA()
			// 归一化到 [0, 1]
			r_norm := float64(r>>8) / 255.0
			g_norm := float64(g>>8) / 255.0
			b_norm := float64(b>>8) / 255.0

			// 标准化
			r_std := (r_norm - mean[0]) / std[0]
			g_std := (g_norm - mean[1]) / std[1]
			b_std := (b_norm - mean[2]) / std[2]

			// HWC -> CHW 转换
			chwData[0*targetSize*targetSize+y*targetSize+x] = r_std
			chwData[1*targetSize*targetSize+y*targetSize+x] = g_std
			chwData[2*targetSize*targetSize+y*targetSize+x] = b_std
		}
	}
    
    // onnxruntime_go 需要 float32，但 gonum 主要用 float64。
    // 在传入 onnx 前需要转换。这里先用 float64 构建。
	// 创建一个 [1, 3, targetSize, targetSize] 的张量需要 reshaping，
    // onnxruntime_go可以直接接受一个 flat slice 和 shape。
    // 这里先返回一个 flat slice 或者一个 Gonum 矩阵（[3, H*W]）
    // 为了简单起见，我们直接返回一个可以被塑形的 flat slice
	// Gonum 矩阵的创建方式是：
	// imageMatrix := mat.NewDense(3, targetSize*targetSize, chwData)
    // 但为了和 ONNX 交互，扁平化的 slice 更直接。
    
    // 这里直接返回扁平化数据，后续步骤再处理成 onnxruntime 需要的格式
	return mat.NewDense(3, targetSize*targetSize, chwData), nil 
}
```

#### 第三步：加载 ONNX 模型并执行推理

使用 `onnxruntime_go` 来加载模型并运行。

**关键代码框架 (main.go):**
```go
import (
	"github.com/yalue/onnxruntime_go"
)

func runInference(session *onnxruntime_go.Session, inputData []float32, inputShape []int64) ([]float32, error) {
	// 将输入数据包装成 onnxruntime 的张量
	inputTensor, err := onnxruntime_go.NewTensor(inputShape, inputData)
	if err != nil {
		return nil, err
	}
	defer inputTensor.Destroy()

	// 准备输入输出名称
	inputName := session.GetInputNames()[0]
	outputName := session.GetOutputNames()[0]

	// 运行模型
	outputTensor, err := session.Run(
		[]onnxruntime_go.Tensor{inputTensor}, // 输入
		[]string{outputName},                // 期望的输出名
		[]string{inputName},                 // 输入名
	)
	if err != nil {
		return nil, err
	}
	defer outputTensor[0].Destroy()

	// 返回结果
	return outputTensor[0].GetData().([]float32), nil
}
```

#### 第四步：实现特征后处理与相似度计算

这部分再次使用 `gonum`。

**关键代码框架 (main.go):**
```go
import "gonum.org/v1/gonum/mat"
import "math"

// l2NormalizeRows 对矩阵的每一行进行 L2 归一化
func l2NormalizeRows(m *mat.Dense) {
	r, _ := m.Dims()
	for i := 0; i < r; i++ {
		rowVec := m.RowView(i)
		norm := mat.Norm(rowVec, 2)
		if norm > 0 {
			rowVec.ScaleVec(1/norm, rowVec)
		}
	}
}

// Softmax 计算 softmax
func softmax(m *mat.Dense) *mat.Dense {
    r, c := m.Dims()
    result := mat.NewDense(r, c, nil)
    
    for i := 0; i < r; i++ {
        row := m.RawRowView(i)
        
        // 减去最大值以保证数值稳定性
        maxVal := row[0]
        for j := 1; j < c; j++ {
            if row[j] > maxVal {
                maxVal = row[j]
            }
        }
        
        var sumExp float64
        expVals := make([]float64, c)
        for j, val := range row {
            expVal := math.Exp(val - maxVal)
            expVals[j] = expVal
            sumExp += expVal
        }
        
        for j, expVal := range expVals {
            result.Set(i, j, expVal / sumExp)
        }
    }
    return result
}


// ... 在你的 main 函数中 ...
func main() {
    // ... 前面的所有步骤 ...

    // 假设 imageFeaturesData 和 textFeaturesData 是从 ONNX 推理得到的 []float32
    // 需要将它们转换成 gonum 的 mat.Dense
    // imageFeatures: 1x512, textFeatures: 4x512
    imgFeatures := mat.NewDense(1, 512, float32ToFloat64(imageFeaturesData))
    txtFeatures := mat.NewDense(4, 512, float32ToFloat64(textFeaturesData))

    // L2 归一化
    l2NormalizeRows(imgFeatures)
    l2NormalizeRows(txtFeatures)

    // logits = 100 * image_features @ text_features.T
    var logits mat.Dense
    logits.Mul(imgFeatures, txtFeatures.T())
    logits.Scale(100, &logits)

    // 计算 softmax
    probs := softmax(&logits)

    // 打印结果
    // mat.Formatted 可以优雅地打印矩阵
    ft := mat.Formatted(probs, mat.Prefix(""), mat.Squeeze())
	  fmt.Printf("%.4f\n", ft)
}

func float32ToFloat64(slice []float32) []float64 {
	newSlice := make([]float64, len(slice))
	for i, v := range slice {
		newSlice[i] = float64(v)
	}
	return newSlice
}
```

### 总结

将这个项目重构为 Go 是一个非常有价值的实践，能带来显著的性能和部署优势。

-   **最大挑战**: 手动移植 `bert_tokenizer.py`。请务必细心，特别是处理 Unicode 和实现 WordPiece 贪心算法时。建议你为移植后的 `tokenizer` 编写单元测试，用 Python 脚本的输出作为标准答案，确保行为一致。
-   **主要工具**: `gonum/mat` 是你的核心伙伴，用于所有 `numpy` 的操作。`yalue/onnxruntime_go` 则负责与 ONNX 模型交互。
-   **数据类型**: 注意 `gonum` 主要使用 `float64`，而 ONNX Runtime 和底层模型通常使用 `float32`。你需要在两者之间进行转换。

按照以上步骤和关键代码，你可以指导你的 AI 助手逐步完成这个重构任务。从最难的分词器开始，步步为营，最终得到一个功能完整的 Go 应用。