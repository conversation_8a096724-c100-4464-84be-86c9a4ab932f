import json
import argparse
from typing import List, Dict
import numpy as np
from PIL import Image
import onnxruntime
from clip import image_transform, tokenize, _MODEL_INFO

class ImageTextSearch:
    def __init__(self, img_model_path: str, txt_model_path: str, model_arch: str = "ViT-B-16"):
        self.model_arch = model_arch
        # 初始化图像预处理
        self.preprocess = image_transform(_MODEL_INFO[model_arch]['input_resolution'])
        
        # 加载图像模型
        img_sess_options = onnxruntime.SessionOptions()
        img_sess_options.log_severity_level = 2
        self.img_session = onnxruntime.InferenceSession(
            img_model_path,
            sess_options=img_sess_options
        )

        # 加载文本模型
        txt_sess_options = onnxruntime.SessionOptions()
        txt_sess_options.log_severity_level = 2
        self.txt_session = onnxruntime.InferenceSession(
            txt_model_path,
            sess_options=txt_sess_options
        )

        # 向量库
        self.vector_db = {
            'images': [],  # 图像向量列表
            'texts': [],   # 文本向量列表
            'metadata': [] # 元数据列表
        }

    def extract_image_features(self, image_path: str) -> np.ndarray:
        """提取图像特征向量"""
        image = self.preprocess(Image.open(image_path))
        image_features = self.img_session.run(["unnorm_image_features"], {"image": image})[0]
        # L2归一化
        image_features = image_features / np.linalg.norm(image_features, axis=-1, keepdims=True)
        return image_features

    def extract_text_features(self, text: str) -> np.ndarray:
        """提取文本特征向量"""
        text_tokens = tokenize([text], context_length=52)
        text_features = self.txt_session.run(["unnorm_text_features"], {"text": text_tokens})[0]
        # L2归一化
        text_features = text_features / np.linalg.norm(text_features, axis=-1, keepdims=True)
        return text_features

    def build_vector_db(self, data: List[Dict]):
        """构建向量库"""
        for item in data:
            # 提取图像向量
            img_vector = self.extract_image_features(item['img'])
            self.vector_db['images'].append(img_vector)

            # 提取文本向量
            txt_vector = self.extract_text_features(item['zi'])
            self.vector_db['texts'].append(txt_vector)

            # 保存元数据
            self.vector_db['metadata'].append(item)

        # 将向量列表转换为numpy数组
        self.vector_db['images'] = np.vstack(self.vector_db['images'])
        self.vector_db['texts'] = np.vstack(self.vector_db['texts'])

    def cosine_similarity(self, query_vector: np.ndarray, target_vectors: np.ndarray) -> np.ndarray:
        """计算余弦相似度"""
        # 由于向量已经归一化，直接计算内积即可
        similarity = query_vector @ target_vectors.T
        return similarity[0]  # 返回一维数组

    def compute_semantic_similarity_scores(self, image_features: np.ndarray, text_features_stack: np.ndarray) -> np.ndarray:
        """
        计算一张图片和多个文本之间的语义相似度分数 (softmax probabilities)
        image_features: (1, D)
        text_features_stack: (N, D)
        """
        # 计算内积
        raw_logits = image_features @ text_features_stack.T  # (1, N)
        # 使用temperature缩放 (logit_scale.exp())
        logits = 100 * raw_logits
        # 计算softmax
        exp_logits = np.exp(logits - np.max(logits, axis=-1, keepdims=True))
        softmax_probs = exp_logits / np.sum(exp_logits, axis=-1, keepdims=True)
        return softmax_probs[0]  # 返回 (N,) 概率数组

    def search(self, query_image_path: str, top_k: int = 5):
        """搜索功能"""
        # 第一阶段：检索 - 使用余弦相似度找到最相似的图片
        query_vector = self.extract_image_features(query_image_path)
        img_similarities = self.cosine_similarity(query_vector, self.vector_db['images'])
        
        # 获取前top_k个最相似的索引
        top_indices = np.argsort(img_similarities)[-top_k:][::-1]
        
        # 第二阶段：重排
        # 1. 参考clip.py，将top_k个文本向量堆叠在一起，与输入图片计算相似度
        top_k_text_vectors = self.vector_db['texts'][top_indices]
        semantic_scores = self.compute_semantic_similarity_scores(query_vector, top_k_text_vectors)
        
        # 2. 按照 0.6*图片相似度得分+0.4*语义相似度得分，重排
        results = []
        for i, idx in enumerate(top_indices):
            img_similarity = img_similarities[idx]
            semantic_similarity = semantic_scores[i]
            
            combined_score = 0.6 * img_similarity + 0.4 * semantic_similarity
            
            results.append({
                'metadata': self.vector_db['metadata'][idx],
                'combined_score': combined_score,
                'img_similarity': float(img_similarity),
                'semantic_similarity': float(semantic_similarity)
            })
            
        # 根据综合得分对结果进行排序
        results.sort(key=lambda x: x['combined_score'], reverse=True)
            
        return results

def main():
    parser = argparse.ArgumentParser(description='图文搜索工具')
    parser.add_argument('query_image', help='查询图片路径')
    parser.add_argument('--json_path', default='search.json', help='搜索数据库JSON文件路径')
    parser.add_argument('--img_model', default='/Users/<USER>/work/hanzi/py/model/clip/vit-b-16.img.fp16.onnx', help='图像模型路径')
    parser.add_argument('--txt_model', default='/Users/<USER>/work/hanzi/py/model/clip/vit-b-16.txt.fp16.onnx', help='文本模型路径')
    args = parser.parse_args()

    # 初始化搜索器
    searcher = ImageTextSearch(args.img_model, args.txt_model)

    # 读取数据库
    with open(args.json_path, 'r', encoding='utf-8') as f:
        data = json.load(f)

    # 构建向量库
    print("正在构建向量库...")
    searcher.build_vector_db(data)

    # 执行搜索
    print(f"\n使用图片 {args.query_image} 搜索...")
    results = searcher.search(args.query_image)

    # 打印结果
    print("\n搜索结果：")
    for i, result in enumerate(results, 1):
        print(f"\n第 {i} 名：")
        print(f"图片路径: {result['metadata']['img']}")
        print(f"文字: {result['metadata']['zi']}")
        print(f"---")
        print(f"综合得分: {result['combined_score']:.4f}")
        print(f"  - 图片相似度 (权重 0.6): {result['img_similarity']:.4f}")
        print(f"  - 语义相似度 (权重 0.4): {result['semantic_similarity']:.4f}")

if __name__ == '__main__':
    main() 