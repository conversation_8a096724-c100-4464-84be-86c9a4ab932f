import cv2
import numpy as np
import os
import matplotlib.pyplot as plt

def visualize_gaps(image, gaps, output_dir, filename="_all_gaps.png"):
    """
    可视化所有找到的空白间隙。
    用不同颜色标记间隙：
    - 蓝色：所有检测到的间隙
    - 红色：间隙的中心线
    """
    visual_image = image.copy()
    
    # 在每个间隙位置画矩形和中心线
    for gap in gaps:
        start = gap['start']
        size = gap['size']
        # 画蓝色矩形表示间隙区域
        cv2.rectangle(visual_image, 
                     (0, start), 
                     (visual_image.shape[1], start + size), 
                     (255, 0, 0), 1)  # 蓝色
        
        # 画红色中心线
        center_y = start + size // 2
        cv2.line(visual_image, 
                 (0, center_y), 
                 (visual_image.shape[1], center_y), 
                 (0, 0, 255), 1)  # 红色
        
        # 在左侧添加间隙大小标注
        cv2.putText(visual_image, 
                    f"size: {size}", 
                    (10, start + size // 2), 
                    cv2.FONT_HERSHEY_SIMPLEX, 
                    0.5, 
                    (0, 255, 0), 
                    1)

    # 保存可视化结果
    output_path = os.path.join(output_dir, filename)
    cv2.imwrite(output_path, visual_image)
    print(f"    -> 已保存间隙可视化图: {output_path}")

def split_image_by_consistent_gaps(image_path,
                                   output_dir='split_consistent_output',
                                   top_n=10,
                                   consistency_threshold=3,
                                   blank_threshold=60,
                                   gap_size=15
                                   ):
    """
    根据新的、更精确的规则切割图像：
    1. 找到所有水平空白间隙。
    2. 筛选出与指定gap_size相差不超过2像素的间隙。
    3. 在这些位置进行切割。

    Args:
        image_path (str): 输入图像的路径。
        output_dir (str): 保存结果的文件夹。
        gap_size (int): 目标间隙大小，会寻找与此大小相近的间隙。
        blank_threshold (int): 定义一个行为"空白"的像素和阈值。
    """
    # --- 1. 准备工作 ---
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)

    original_image = cv2.imread(image_path)
    if original_image is None:
        print(f"错误: 无法加载图像 '{image_path}'")
        return

    print(">>> 步骤 1: 预处理图像并计算水平投影...")
    gray = cv2.cvtColor(original_image, cv2.COLOR_BGR2GRAY)
    _, binary = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY_INV + cv2.THRESH_OTSU)
    horizontal_projection = np.sum(binary, axis=1)

    # --- 2. 找到所有空白间隙 ---
    print(">>> 步骤 2: 查找所有水平空白间隙...")
    all_gaps = []
    current_gap_size = 0
    current_gap_start = -1

    for i, proj_val in enumerate(horizontal_projection):
        if proj_val <= blank_threshold:
            if current_gap_size == 0:
                current_gap_start = i
            current_gap_size += 1
        else:
            if current_gap_size > consistency_threshold:
                # 一个间隙结束，记录下来
                all_gaps.append({'start': current_gap_start, 'size': current_gap_size})
            current_gap_size = 0
            current_gap_start = -1

    # 别忘了检查最后一个可能的间隙
    if current_gap_size > 0:
        all_gaps.append({'start': current_gap_start, 'size': current_gap_size})

    if not all_gaps:
        print("警告: 未找到任何空白间隙。")
        return

    print(f"    -> 共找到 {len(all_gaps)} 个空白间隙。")
    
    # 可视化所有找到的间隙
    visualize_gaps(original_image, all_gaps, output_dir, "_all_gaps.png")

    # --- 3. 筛选符合条件的间隙 ---
    print(f">>> 步骤 3: 筛选与目标间隙大小({gap_size})相差不超过2像素的间隙...")
    selected_gaps = [gap for gap in all_gaps if abs(gap['size'] - gap_size) <= 2]
    
    if not selected_gaps:
        print(f"警告: 未找到与目标大小({gap_size})相差不超过2像素的间隙。")
        return

    print(f"    -> 找到 {len(selected_gaps)} 个符合条件的间隙")
    print(f"    -> 这些间隙的高度分别为: {[g['size'] for g in selected_gaps]}")
    
    # 可视化筛选后的间隙
    visualize_gaps(original_image, selected_gaps, output_dir, "_selected_gaps.png")

    # --- 4. 执行切割 ---
    cut_points = [g['start'] + g['size'] // 2 for g in selected_gaps]
    cut_points.sort() # 确保从上到下切割

    print(f"    -> 将在以下 y 坐标处切割: {cut_points}")

    last_y = 0
    visual_image = original_image.copy()

    for i, cut_y in enumerate(cut_points):
        # 切割并保存
        part = original_image[last_y:cut_y, :]
        filename = os.path.join(output_dir, f"part_{i+1:02d}.png")
        cv2.imwrite(filename, part)
        print(f"        保存片段: {filename}")

        # 在可视化图上画线
        cv2.line(visual_image, (0, cut_y), (visual_image.shape[1], cut_y), (0, 255, 0), 2)
        last_y = cut_y

    # 保存最后一个片段
    final_part = original_image[last_y:, :]
    final_filename = os.path.join(output_dir, f"part_{len(cut_points)+1:02d}.png")
    cv2.imwrite(final_filename, final_part)
    print(f"        保存片段: {final_filename}")

    # 保存可视化结果
    visual_filename = os.path.join(output_dir, "_visualized_cuts.png")
    cv2.imwrite(visual_filename, visual_image)
    print(f"    -> 已保存带切割线的可视化图: {visual_filename}")



if __name__ == '__main__':
    # --- 配置 ---
    INPUT_IMAGE_PATH = 'img.png'

    if not os.path.exists(INPUT_IMAGE_PATH):
        print(f"输入文件不存在: '{INPUT_IMAGE_PATH}'，请检查文件名或路径。")
    else:
        split_image_by_consistent_gaps(INPUT_IMAGE_PATH)